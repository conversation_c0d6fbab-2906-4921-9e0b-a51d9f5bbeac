# LONI Ollama Model Management System

A comprehensive Ollama model management system for the LONI platform that provides model discovery, installation, and management capabilities through both API endpoints and interactive CLI tools.

## Features

### 🔍 Model Discovery
- **Web Scraping**: Automatically discovers models from https://ollama.com/search
- **Structured Data**: Extracts model metadata including descriptions, sizes, capabilities
- **Caching**: 24-hour cache to avoid excessive scraping
- **Search & Filter**: Support for search queries and category filtering

### 📦 Model Management
- **Installation**: Download and install models with progress tracking
- **Uninstallation**: Remove models and clean up storage
- **Status Tracking**: Real-time status monitoring and progress updates
- **Usage Statistics**: Track model usage and download counts

### 🖥️ Interactive CLI
- **Model Selection**: Numbered list interface for easy model selection
- **Size Options**: Checkbox interface for selecting model variants
- **Progress Display**: Real-time download progress with rich formatting
- **Validation**: Input validation and confirmation prompts

### 🌐 REST API
- **Discovery Endpoint**: `/api/models/available` with pagination
- **Management Endpoints**: Install, uninstall, status, and progress tracking
- **Real-time Updates**: WebSocket support for progress monitoring

## Quick Start

### 1. Interactive Model Selection

```bash
# Navigate to backend directory
cd loni/apps/backend

# Run interactive model selection
python manage.py models select

# List installed models
python manage.py models select --list-installed
```

### 2. API Usage

```bash
# Discover available models
curl "http://localhost:8000/api/models/available?page=1&page_size=10"

# Install a model
curl -X POST "http://localhost:8000/api/models/install" \
  -H "Content-Type: application/json" \
  -d '{"model_id": "llama3.2:latest"}'

# Check installation status
curl "http://localhost:8000/api/models/status/llama3.2:latest"

# List installed models
curl "http://localhost:8000/api/models/installed"
```

### 3. CLI Commands

```bash
# List all available commands
python manage.py models --help

# Install a specific model
python manage.py models install llama3.2:latest

# Uninstall a model
python manage.py models uninstall llama3.2:latest

# Check model status
python manage.py models status llama3.2:latest

# List models in different formats
python manage.py models list --format json
python manage.py models list --installed-only
```

## Architecture

### Components

1. **Discovery Service** (`discovery.py`)
   - Web scraping and data extraction
   - Model metadata parsing
   - Caching and rate limiting

2. **Management Service** (`management.py`)
   - Model installation and removal
   - Progress tracking and status management
   - Database integration

3. **API Routes** (`ollama_routes.py`)
   - RESTful endpoints for all operations
   - Request/response validation
   - Error handling and logging

4. **CLI Tool** (`model_cli.py`)
   - Interactive user interface
   - Rich formatting and progress display
   - Input validation and confirmation

5. **Database Models** (`ai_model.py`)
   - Model registry and metadata storage
   - Usage tracking and statistics
   - Status and installation tracking

### Database Schema

The `ollama_models` table stores:

- **Basic Info**: name, tag, full_name, description
- **Metadata**: capabilities, parameter_count, download_size_gb
- **Status**: installation status, file paths, sizes
- **Usage**: download_count, usage_count, last_used_at
- **Registry**: registry_url, last_updated_registry

### API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/models/available` | GET | Discover available models with pagination |
| `/api/models/installed` | GET | List installed models |
| `/api/models/install` | POST | Install a model |
| `/api/models/uninstall/{model_id}` | DELETE | Uninstall a model |
| `/api/models/status/{model_id}` | GET | Get model status |
| `/api/models/progress/{model_id}` | GET | Get download progress |
| `/api/models/cancel/{model_id}` | POST | Cancel download |

## Configuration

### Environment Variables

```bash
# Ollama Configuration
OLLAMA_BASE_URL=http://ollama:11434
OLLAMA_PORT=11434

# Model Storage
MODELS_DIR=loni/data/models/ollama

# Cache Settings
MODEL_CACHE_DURATION=24  # hours
```

### Docker Integration

The system integrates with the existing Docker infrastructure:

```yaml
# Ollama service is already configured in docker-compose.yml
ollama-cpu:
  image: ollama/ollama:latest
  container_name: loni-ollama-cpu
  ports:
    - "11434:11434"
  volumes:
    - ollama_data:/root/.ollama
    - ./config/ollama:/etc/ollama
```

## Usage Examples

### Interactive Model Selection

```bash
$ python manage.py models select

┌─────────────────────────────────────────────────────────────┐
│                    LONI Ollama Model Manager                │
│          Interactive model discovery and installation tool  │
└─────────────────────────────────────────────────────────────┘

⠋ Discovering available models...

                        Available Ollama Models                         
┏━━━━┳━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━┳━━━━━━━━━━┓
┃ #  ┃ Name          ┃ Description                    ┃ Sizes      ┃ Size(GB) ┃
┡━━━━╇━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━╇━━━━━━━━━━┩
│ 1  │ llama3.2      │ Meta's Llama 3.2 model...     │ 8B, 70B    │ 4.5      │
│ 2  │ mistral       │ Mistral 7B model for chat     │ 7B         │ 4.0      │
│ 3  │ codellama     │ Code generation model          │ 7B, 13B    │ 4.0      │
└────┴───────────────┴────────────────────────────────┴────────────┴──────────┘

Select a model to install:
Enter model number (1-3) or 'q' to quit [q]: 1
```

### API Integration

```python
import httpx

async def install_model(model_id: str):
    async with httpx.AsyncClient() as client:
        # Start installation
        response = await client.post(
            "http://localhost:8000/api/models/install",
            json={"model_id": model_id}
        )
        
        if response.status_code == 200:
            # Monitor progress
            while True:
                status_response = await client.get(
                    f"http://localhost:8000/api/models/status/{model_id}"
                )
                status = status_response.json()
                
                if status["status"] == "downloaded":
                    print(f"✅ {model_id} installed successfully!")
                    break
                elif status["status"] == "error":
                    print(f"❌ Installation failed: {status.get('message')}")
                    break
                
                # Show progress
                if progress := status.get("download_progress"):
                    print(f"📥 {progress['percent']:.1f}% - {progress['status']}")
                
                await asyncio.sleep(2)

# Usage
await install_model("llama3.2:latest")
```

## Error Handling

The system includes comprehensive error handling:

- **Network Errors**: Graceful handling of connection failures
- **Disk Space**: Validation of available storage before download
- **Model Conflicts**: Prevention of duplicate installations
- **Cancellation**: Clean cancellation of ongoing downloads
- **Validation**: Input validation for model IDs and parameters

## Performance Considerations

- **Caching**: 24-hour cache for model discovery to reduce API calls
- **Async Operations**: Non-blocking downloads and API operations
- **Progress Tracking**: Real-time progress updates without blocking
- **Resource Management**: Proper cleanup of connections and resources

## Security

- **Input Validation**: All user inputs are validated and sanitized
- **Path Safety**: File paths are validated to prevent directory traversal
- **Rate Limiting**: Built-in rate limiting for discovery operations
- **Error Sanitization**: Error messages don't expose sensitive information

## Troubleshooting

### Common Issues

1. **Connection Errors**
   ```bash
   # Check Ollama service status
   docker logs loni-ollama-cpu
   curl http://localhost:11434/api/tags
   ```

2. **Permission Errors**
   ```bash
   # Check directory permissions
   ls -la loni/data/models/ollama/
   ```

3. **Disk Space**
   ```bash
   # Check available space
   df -h loni/data/models/
   ```

### Debug Mode

```bash
# Enable verbose logging
python manage.py --verbose models select

# Check logs
tail -f loni/data/logs/containers/backend.log
```

## Contributing

When extending the model management system:

1. **Follow Patterns**: Use existing async/await patterns
2. **Add Tests**: Include unit tests for new functionality
3. **Update Schemas**: Modify Pydantic schemas for API changes
4. **Document Changes**: Update this README and API documentation
5. **Error Handling**: Include comprehensive error handling

## Dependencies

- **FastAPI**: Web framework and API routing
- **SQLAlchemy**: Database ORM and migrations
- **httpx**: Async HTTP client for web scraping
- **BeautifulSoup**: HTML parsing for model discovery
- **Rich**: CLI formatting and progress display
- **Click**: Command-line interface framework
- **Pydantic**: Data validation and serialization

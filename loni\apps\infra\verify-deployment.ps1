# LONI Platform Deployment Verification Script
# Comprehensive health check for all services

param(
    [switch]$Detailed,
    [switch]$GPU,
    [switch]$Performance
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"
$Magenta = "Magenta"

function Write-Header {
    param([string]$Title)
    Write-Host ""
    Write-Host "🔍 $Title" -ForegroundColor $Magenta
    Write-Host ("=" * ($Title.Length + 3)) -ForegroundColor $Magenta
}

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor $Red
}

function Test-ServiceHealth {
    param([string]$ServiceName, [string]$URL, [int]$ExpectedStatus = 200)
    
    try {
        $response = Invoke-WebRequest -Uri $URL -Method Get -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq $ExpectedStatus) {
            Write-Success "$ServiceName is healthy (HTTP $($response.StatusCode))"
            return $true
        } else {
            Write-Warning "$ServiceName returned HTTP $($response.StatusCode)"
            return $false
        }
    } catch {
        Write-Error "$ServiceName is not accessible: $($_.Exception.Message)"
        return $false
    }
}

function Get-ContainerStatus {
    Write-Header "Container Status"
    
    $containers = docker compose ps --format "json" | ConvertFrom-Json
    $totalContainers = $containers.Count
    $runningContainers = 0
    $healthyContainers = 0
    
    foreach ($container in $containers) {
        $status = $container.State
        $health = $container.Health
        
        if ($status -eq "running") {
            $runningContainers++
            
            if ($health -eq "healthy" -or $health -eq "") {
                $healthyContainers++
                Write-Success "$($container.Service): $status $(if($health) {"($health)"})"
            } else {
                Write-Warning "$($container.Service): $status ($health)"
            }
        } else {
            Write-Error "$($container.Service): $status"
        }
    }
    
    Write-Status "Summary: $runningContainers/$totalContainers running, $healthyContainers healthy"
    return @{
        Total = $totalContainers
        Running = $runningContainers
        Healthy = $healthyContainers
    }
}

function Test-ServiceEndpoints {
    Write-Header "Service Endpoint Health Checks"
    
    $services = @(
        @{ Name = "Backend Health"; URL = "http://localhost:8000/health" },
        @{ Name = "Frontend"; URL = "http://localhost:3000" },
        @{ Name = "Ollama API"; URL = "http://localhost:11434/api/tags" },
        @{ Name = "Prometheus"; URL = "http://localhost:9090/-/healthy" },
        @{ Name = "Grafana"; URL = "http://localhost:3001/api/health" },
        @{ Name = "Jaeger"; URL = "http://localhost:16686/" }
    )
    
    $healthyServices = 0
    foreach ($service in $services) {
        if (Test-ServiceHealth -ServiceName $service.Name -URL $service.URL) {
            $healthyServices++
        }
    }
    
    Write-Status "Endpoint Summary: $healthyServices/$($services.Count) services responding"
    return $healthyServices -eq $services.Count
}

function Test-DatabaseConnections {
    Write-Header "Database Connection Tests"
    
    # Test PostgreSQL
    try {
        $pgTest = docker exec loni-postgres pg_isready -U loni 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "PostgreSQL is accepting connections"
        } else {
            Write-Error "PostgreSQL connection failed"
        }
    } catch {
        Write-Error "Could not test PostgreSQL connection"
    }
    
    # Test Redis
    try {
        $redisTest = docker exec loni-redis redis-cli ping 2>$null
        if ($redisTest -eq "PONG") {
            Write-Success "Redis is responding"
        } else {
            Write-Error "Redis connection failed"
        }
    } catch {
        Write-Error "Could not test Redis connection"
    }
    
    # Test Qdrant
    if (Test-ServiceHealth -ServiceName "Qdrant" -URL "http://localhost:6333/health") {
        # Additional Qdrant-specific test
    }
}

function Test-OllamaIntegration {
    Write-Header "Ollama Integration Tests"
    
    # Test Ollama API
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:11434/api/tags" -Method Get -TimeoutSec 10
        Write-Success "Ollama API is responding"
        
        if ($response.models -and $response.models.Count -gt 0) {
            Write-Success "Found $($response.models.Count) installed models"
            foreach ($model in $response.models) {
                Write-Status "  - $($model.name) ($([math]::Round($model.size / 1GB, 2)) GB)"
            }
        } else {
            Write-Warning "No models installed in Ollama"
        }
    } catch {
        Write-Error "Ollama API test failed: $($_.Exception.Message)"
    }
    
    # Test backend-to-Ollama connectivity
    try {
        $backendTest = docker exec loni-backend curl -f http://ollama:11434/api/tags 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Backend can connect to Ollama"
        } else {
            Write-Error "Backend cannot connect to Ollama"
        }
    } catch {
        Write-Error "Could not test backend-Ollama connectivity"
    }
    
    # Test model management API
    if (Test-ServiceHealth -ServiceName "Model Management API" -URL "http://localhost:8000/api/models/available") {
        Write-Success "Model management API is working"
    }
}

function Test-GPUSupport {
    if (-not $GPU) { return }
    
    Write-Header "GPU Support Verification"
    
    # Check NVIDIA drivers
    try {
        $nvidiaInfo = nvidia-smi --query-gpu=name,memory.total,memory.used,utilization.gpu --format=csv,noheader,nounits
        Write-Success "NVIDIA GPU detected: $nvidiaInfo"
    } catch {
        Write-Error "NVIDIA drivers not working"
        return
    }
    
    # Check Docker GPU support
    try {
        $dockerGPU = docker run --rm --gpus all nvidia/cuda:12.0-base-ubuntu20.04 nvidia-smi 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Docker GPU support is working"
        } else {
            Write-Error "Docker GPU support failed"
        }
    } catch {
        Write-Error "Could not test Docker GPU support"
    }
    
    # Check Ollama GPU usage
    try {
        $ollamaLogs = docker logs loni-ollama --tail 20 2>$null | Select-String "GPU\|CUDA\|nvidia"
        if ($ollamaLogs) {
            Write-Success "Ollama is using GPU acceleration"
            foreach ($line in $ollamaLogs) {
                Write-Status "  $line"
            }
        } else {
            Write-Warning "No GPU usage detected in Ollama logs"
        }
    } catch {
        Write-Warning "Could not check Ollama GPU usage"
    }
}

function Get-PerformanceMetrics {
    if (-not $Performance) { return }
    
    Write-Header "Performance Metrics"
    
    # Container resource usage
    Write-Status "Container Resource Usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}"
    
    # System resources
    Write-Status "System Resources:"
    $memory = Get-WmiObject -Class Win32_OperatingSystem
    $totalRAM = [math]::Round($memory.TotalVisibleMemorySize / 1MB, 2)
    $freeRAM = [math]::Round($memory.FreePhysicalMemory / 1MB, 2)
    $usedRAM = $totalRAM - $freeRAM
    
    Write-Status "  RAM: $usedRAM GB / $totalRAM GB used ($([math]::Round(($usedRAM/$totalRAM)*100, 1))%)"
    
    # Disk usage
    $disk = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
    $totalDisk = [math]::Round($disk.Size / 1GB, 2)
    $freeDisk = [math]::Round($disk.FreeSpace / 1GB, 2)
    $usedDisk = $totalDisk - $freeDisk
    
    Write-Status "  Disk: $usedDisk GB / $totalDisk GB used ($([math]::Round(($usedDisk/$totalDisk)*100, 1))%)"
    
    if ($GPU) {
        try {
            $gpuInfo = nvidia-smi --query-gpu=utilization.gpu,memory.used,memory.total,temperature.gpu --format=csv,noheader,nounits
            Write-Status "  GPU: $gpuInfo"
        } catch {
            Write-Warning "Could not get GPU metrics"
        }
    }
}

function Show-Summary {
    param($ContainerStats, $EndpointsHealthy)
    
    Write-Header "Deployment Summary"
    
    $overallHealth = $true
    
    # Container health
    if ($ContainerStats.Running -eq $ContainerStats.Total -and $ContainerStats.Healthy -eq $ContainerStats.Total) {
        Write-Success "All containers are running and healthy"
    } else {
        Write-Warning "Some containers are not healthy"
        $overallHealth = $false
    }
    
    # Endpoint health
    if ($EndpointsHealthy) {
        Write-Success "All service endpoints are responding"
    } else {
        Write-Warning "Some service endpoints are not responding"
        $overallHealth = $false
    }
    
    # Overall status
    if ($overallHealth) {
        Write-Host ""
        Write-Host "🎉 LONI Platform is fully operational!" -ForegroundColor $Green
        Write-Host ""
        Write-Host "🚀 Next Steps:" -ForegroundColor $Blue
        Write-Host "  1. Install AI models: docker exec -it loni-backend python manage.py models select" -ForegroundColor $Blue
        Write-Host "  2. Access frontend: http://localhost:3000" -ForegroundColor $Blue
        Write-Host "  3. Check monitoring: http://localhost:3001 (Grafana)" -ForegroundColor $Blue
    } else {
        Write-Host ""
        Write-Host "⚠️  LONI Platform has some issues that need attention" -ForegroundColor $Yellow
        Write-Host ""
        Write-Host "🔧 Troubleshooting:" -ForegroundColor $Blue
        Write-Host "  1. Check logs: docker compose logs [service-name]" -ForegroundColor $Blue
        Write-Host "  2. Restart services: docker compose restart [service-name]" -ForegroundColor $Blue
        Write-Host "  3. Full restart: docker compose down && docker compose up -d" -ForegroundColor $Blue
    }
}

# Main execution
Write-Host "🔍 LONI Platform Deployment Verification" -ForegroundColor $Green
Write-Host "=======================================" -ForegroundColor $Green

# Run verification tests
$containerStats = Get-ContainerStatus
$endpointsHealthy = Test-ServiceEndpoints
Test-DatabaseConnections
Test-OllamaIntegration

if ($GPU) {
    Test-GPUSupport
}

if ($Performance) {
    Get-PerformanceMetrics
}

# Show summary
Show-Summary -ContainerStats $containerStats -EndpointsHealthy $endpointsHealthy

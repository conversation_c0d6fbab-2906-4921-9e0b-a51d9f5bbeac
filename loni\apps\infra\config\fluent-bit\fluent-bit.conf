# Fluent Bit Configuration for LONI Platform
# Centralized logging with error-only filtering and AI-friendly output

[SERVICE]
    Flush         5
    Daemon        off
    Log_Level     info
    Parsers_File  parsers.conf
    Plugins_File  plugins.conf
    HTTP_Server   On
    HTTP_Listen   0.0.0.0
    HTTP_Port     2020
    storage.path  /tmp/flb-storage/
    storage.sync  normal
    storage.checksum off
    storage.backlog.mem_limit 5M

# Input: Docker container logs via forward protocol
[INPUT]
    Name              forward
    Listen            0.0.0.0
    Port              24224
    Buffer_Chunk_Size 1M
    Buffer_Max_Size   6M

# Input: Application logs from mounted volumes
[INPUT]
    Name              tail
    Path              /fluent-bit/logs/frontend/*.log
    Path_Key          filename
    Tag               frontend
    Parser            json
    Buffer_Chunk_Size 1M
    Buffer_Max_Size   6M
    Skip_Long_Lines   On
    Refresh_Interval  5

# Input: Backend logs from mounted volumes
[INPUT]
    Name              tail
    Path              /fluent-bit/logs/backend/*.log
    Path_Key          filename
    Tag               backend
    Parser            json
    Buffer_Chunk_Size 1M
    Buffer_Max_Size   6M
    Skip_Long_Lines   On
    Refresh_Interval  5

# Input: Nginx logs from mounted volumes
[INPUT]
    Name              tail
    Path              /fluent-bit/logs/nginx/*.log
    Path_Key          filename
    Tag               nginx
    Parser            json
    Buffer_Chunk_Size 1M
    Buffer_Max_Size   6M
    Skip_Long_Lines   On
    Refresh_Interval  5

# Filter: Add metadata to all logs
[FILTER]
    Name              record_modifier
    Match             *
    Record            platform loni
    Record            environment development

# Filter: Error-only filtering for all services
[FILTER]
    Name              grep
    Match             *
    Regex             level (ERROR|FATAL|CRITICAL|error|fatal|critical|Error|Fatal|Critical|Exception|exception|EXCEPTION|failed|Failed|FAILED)

# Output: Structured logs for AI parsing
[OUTPUT]
    Name              file
    Match             *
    Path              /fluent-bit/logs/structured/
    File              ai_logs.log

# Output: Error summary for quick review
[OUTPUT]
    Name              file
    Match             *
    Path              /fluent-bit/logs/summary/
    File              error_summary.log

# Output: Write to stdout for development
[OUTPUT]
    Name              stdout
    Match             *
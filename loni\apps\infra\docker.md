# LONI Platform Docker Infrastructure Documentation

**Version:** 1.0  
**Last Updated:** July 13, 2025  
**Target Audience:** AI Coding Agents  
**Status:** Production-Ready (14/14 services operational)

## Table of Contents

1. [Infrastructure Overview](#infrastructure-overview)
2. [Container Specifications](#container-specifications)
3. [Configuration Management](#configuration-management)
4. [Troubleshooting Guide](#troubleshooting-guide)
5. [Best Practices and Issue Prevention](#best-practices-and-issue-prevention)
6. [Operational Procedures](#operational-procedures)
7. [AI Agent Guidelines](#ai-agent-guidelines)

---

## 1. Infrastructure Overview

### 1.1 Service Inventory (14 Containers)

| Service | Role | Status | Dependencies | Ports |
|---------|------|--------|--------------|-------|
| **loni-backend** | FastAPI application server | ✅ Healthy | postgres, redis, qdrant | 8000 |
| **loni-frontend** | Next.js web application | ✅ Running | backend | 3000 |
| **loni-postgres** | Primary database | ✅ Healthy | None | 5432 |
| **loni-redis** | Cache and session store | ✅ Healthy | None | 6379 |
| **loni-qdrant** | Vector database | ✅ Running | None | 6333-6334 |
| **loni-prometheus** | Metrics collection | ✅ Healthy | exporters, cadvisor | 9090 |
| **loni-grafana** | Monitoring dashboards | ✅ Healthy | prometheus | 3001 |
| **loni-nginx** | Reverse proxy | ✅ Running | frontend, backend, grafana | 80, 443 |
| **loni-jaeger** | Distributed tracing | ✅ Healthy | None | 16686, 14250, 14268, 4317-4318 |
| **loni-fluent-bit** | Log aggregation | ✅ Running | None | 2020, 24224 |
| **loni-cadvisor** | Container metrics | ✅ Healthy | None | 8080 |
| **loni-postgres-exporter** | Database metrics | ✅ Running | postgres | 9187 |
| **loni-redis-exporter** | Cache metrics | ✅ Running | redis | 9121 |
| **loni-n8n** | Workflow automation | ✅ Running | postgres | 5678 |

### 1.2 Network Architecture

**Network:** `loni-network` (**********/16)
- **Internal Communication:** All services communicate via Docker internal DNS
- **External Access:** Only specific ports exposed to host
- **Service Discovery:** Automatic via Docker Compose service names

**Communication Patterns:**
```
Frontend (3000) → Backend (8000) → Database Layer (postgres:5432, redis:6379, qdrant:6333)
Nginx (80/443) → Frontend/Backend/Grafana (reverse proxy)
Prometheus (9090) → Exporters (9187, 9121, 8080) (metrics collection)
Grafana (3001) → Prometheus (9090) (dashboard data)
```

### 1.3 Volume Mounts and Data Persistence

**Persistent Volumes:**
- `postgres_data:/var/lib/postgresql/data` - Database storage
- `qdrant_data:/qdrant/storage` - Vector database storage
- `prometheus_data:/prometheus` - Metrics storage
- `grafana_data:/var/lib/grafana` - Dashboard configurations

**Configuration Mounts:**
- `./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro`
- `./config/grafana/provisioning:/etc/grafana/provisioning:ro`
- `./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro`
- `./config/fluent-bit/fluent-bit.conf:/fluent-bit/etc/fluent-bit.conf:ro`

**Log Directories:**
- `../../data/logs/containers` - Service-specific logs
- `../../data/logs/applications` - Application logs

---

## 2. Container Specifications

### 2.1 Core Application Services

#### Backend (loni-backend)
```yaml
image: loni-backend:latest
build: ../backend (Dockerfile, target: production)
ports: ["8000:8000"]
environment:
  - POSTGRES_HOST=postgres
  - POSTGRES_PORT=5432
  - POSTGRES_DB=loni
  - POSTGRES_USER=loni
  - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
  - REDIS_URL=redis://redis:6379
  - QDRANT_URL=http://qdrant:6333
health_check: curl -f http://localhost:8000/health
dependencies: postgres (healthy), redis (healthy), qdrant (started)
```

#### Frontend (loni-frontend)
```yaml
image: loni-frontend:latest
build: ../frontend (Dockerfile, target: production)
ports: ["3000:3000"]
environment:
  - NODE_ENV=production
  - NEXT_PUBLIC_API_URL=http://backend:8000
  - NEXT_PUBLIC_WS_URL=ws://backend:8000/ws
health_check: curl -f http://localhost:3000/api/health
dependencies: backend (started)
```

### 2.2 Database Layer

#### PostgreSQL (loni-postgres)
```yaml
image: postgres:15-alpine
ports: ["5432:5432"]
environment:
  - POSTGRES_DB=loni
  - POSTGRES_USER=loni
  - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
volumes:
  - postgres_data:/var/lib/postgresql/data
  - ./init/postgres:/docker-entrypoint-initdb.d:ro
health_check: pg_isready -U loni
```

#### Redis (loni-redis)
```yaml
image: redis:7-alpine
ports: ["6379:6379"]
command: redis-server --requirepass ${REDIS_PASSWORD}
volumes:
  - redis_data:/data
health_check: redis-cli -a ${REDIS_PASSWORD} ping
```

#### Qdrant (loni-qdrant)
```yaml
image: qdrant/qdrant:latest
ports: ["6333-6334:6333-6334"]
volumes:
  - qdrant_data:/qdrant/storage
  - ./config/qdrant/config.yaml:/qdrant/config/production.yaml:ro
```

### 2.3 Monitoring Stack

#### Prometheus (loni-prometheus)
```yaml
image: prom/prometheus:latest
ports: ["9090:9090"]
command:
  - '--config.file=/etc/prometheus/prometheus.yml'
  - '--storage.tsdb.path=/prometheus'
  - '--storage.tsdb.retention.time=15d'
  - '--storage.tsdb.retention.size=10GB'
  - '--web.enable-lifecycle'
volumes:
  - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
  - prometheus_data:/prometheus
health_check: wget --spider http://localhost:9090/-/healthy
dependencies: cadvisor, postgres-exporter, redis-exporter
```

#### Grafana (loni-grafana)
```yaml
image: grafana/grafana:latest
ports: ["3001:3000"]
environment:
  - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
volumes:
  - grafana_data:/var/lib/grafana
  - ./config/grafana/provisioning:/etc/grafana/provisioning:ro
health_check: curl -f http://localhost:3000/api/health
dependencies: prometheus (healthy)
```

### 2.4 Infrastructure Services

#### Nginx (loni-nginx)
```yaml
image: nginx:alpine
ports: ["80:80", "443:443"]
volumes:
  - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
  - ./config/nginx/.htpasswd:/etc/nginx/.htpasswd:ro
health_check: nginx -t
dependencies: frontend (healthy), backend (healthy), grafana (healthy)
```

---

## 3. Configuration Management

### 3.1 Configuration File Locations

**Directory Structure:**
```
config/
├── prometheus/
│   ├── prometheus.yml          # Metrics collection configuration
│   └── alert_rules.yml         # Alerting rules
├── grafana/
│   ├── provisioning/
│   │   ├── datasources/        # Prometheus datasource config
│   │   └── dashboards/         # Dashboard definitions
│   └── dashboards/             # Dashboard JSON files
├── nginx/
│   ├── nginx.conf              # Reverse proxy configuration
│   └── .htpasswd               # Basic auth credentials
├── fluent-bit/
│   └── fluent-bit.conf         # Log aggregation configuration
├── qdrant/
│   └── config.yaml             # Vector database configuration
└── redis/
    └── redis.conf              # Cache configuration (optional)
```

### 3.2 Environment Variable Management

**File:** `.env`
```bash
# Database Configuration
POSTGRES_DB=loni
POSTGRES_USER=loni
POSTGRES_PASSWORD=dyzcb5TNANOfoqie8FeMbKtYWF1GQrx8fUNlH8Twq5g=
POSTGRES_PORT=5432

# Cache Configuration
REDIS_PASSWORD=HG2XE5x4eGLVYIWN

# Monitoring Configuration
GRAFANA_ADMIN_PASSWORD=admin123
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001

# Application Configuration
HTTP_PORT=80
HTTPS_PORT=443
```

### 3.3 Critical Configuration Requirements

#### Prometheus Configuration (prometheus.yml)
**CRITICAL:** Do NOT include retention settings in YAML. Use command-line flags only.

❌ **WRONG:**
```yaml
storage:
  tsdb:
    retention.time: 15d
    retention.size: 10GB
```

✅ **CORRECT:**
```yaml
# Storage configuration is handled via command-line flags in docker-compose.yml
# --storage.tsdb.retention.time=15d
# --storage.tsdb.retention.size=10GB
```

#### Backend Database Configuration
**CRITICAL:** Use correct environment variable aliases.

✅ **CORRECT:**
```python
class DatabaseSettings(BaseSettings):
    host: str = "postgres"
    port: int = Field(default=5432, alias="POSTGRES_PORT")
    database: str = Field(default="loni", alias="POSTGRES_DB")
    username: str = Field(default="loni", alias="POSTGRES_USER")
    password: str = Field(..., alias="POSTGRES_PASSWORD")
```

---

## 4. Troubleshooting Guide

### 4.1 Prometheus Configuration Errors

**Issue:** `yaml: unmarshal errors: field retention.time not found in type config.plain`

**Root Cause:** Retention configuration incorrectly placed in YAML file instead of command-line flags.

**Resolution:**
1. Remove retention fields from `config/prometheus/prometheus.yml`
2. Ensure docker-compose.yml has correct command flags:
   ```yaml
   command:
     - '--storage.tsdb.retention.time=15d'
     - '--storage.tsdb.retention.size=10GB'
   ```
3. Restart container: `docker compose up prometheus -d`

### 4.2 Nginx Upstream Resolution Failures

**Issue:** `host not found in upstream "prometheus:9090"`

**Root Cause:** Static upstream resolution fails when dependent service is unavailable.

**Resolution:**
1. Add DNS resolver to nginx.conf:
   ```nginx
   resolver 127.0.0.11 valid=30s;
   ```
2. Use dynamic resolution with variables:
   ```nginx
   location /prometheus/ {
       set $prometheus_upstream prometheus:9090;
       proxy_pass http://$prometheus_upstream/;
   }
   ```
3. Remove static upstream blocks for optional services

### 4.3 Backend ImportError and Database Authentication

**Issue:** `ImportError: cannot import name 'JWTAuthentication'` and `password authentication failed`

**Root Cause:** 
- Deprecated fastapi-users import
- Database credential mismatch

**Resolution:**
1. Update authentication imports:
   ```python
   from fastapi_users.authentication import JWTStrategy  # not JWTAuthentication
   ```
2. Fix database configuration aliases:
   ```python
   username: str = Field(default="loni", alias="POSTGRES_USER")
   password: str = Field(..., alias="POSTGRES_PASSWORD")
   ```
3. Ensure environment variables are passed to container

### 4.4 Service Dependency Issues

**Issue:** Services failing to start due to dependency health checks

**Root Cause:** Circular dependencies or unavailable services

**Resolution:**
1. Use `service_started` instead of `service_healthy` for optional dependencies
2. Remove dependencies on services that may not be available (e.g., node-exporter on Windows)
3. Implement graceful degradation in application code

---

## 5. Best Practices and Issue Prevention

### 5.1 Configuration Validation

**Before Deployment:**
1. Validate YAML syntax: `docker compose config`
2. Check environment variables: `docker compose config | grep -A 10 environment`
3. Verify volume mounts exist: `ls -la config/`
4. Test configuration files individually

### 5.2 Dependency Management

**Startup Sequencing:**
1. Database layer first (postgres, redis, qdrant)
2. Application layer (backend, frontend)
3. Monitoring layer (prometheus, grafana)
4. Infrastructure layer (nginx)

**Health Check Implementation:**
```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 40s
```

### 5.3 Windows-Specific Considerations

**Node Exporter Compatibility:**
- Node exporter fails on Windows due to mount limitations
- Comment out node-exporter targets in prometheus.yml
- Remove node-exporter dependency from prometheus service
- Use alternative host monitoring solutions for Windows

### 5.4 Service Health Check Guidelines

**Implementation Requirements:**
1. All services must have health check endpoints
2. Health checks should verify actual functionality, not just process existence
3. Use appropriate timeouts and retry counts
4. Implement graceful degradation for optional dependencies

---

## 6. Operational Procedures

### 6.1 Complete Deployment Workflow

**From Clean State to Full Operation:**

1. **Environment Setup:**
   ```bash
   cd loni/apps/infra
   cp .env.example .env  # Edit with actual values
   ```

2. **Configuration Validation:**
   ```bash
   docker compose config
   ls -la config/*/
   ```

3. **Database Layer Deployment:**
   ```bash
   docker compose up postgres redis qdrant -d
   docker compose ps  # Verify healthy status
   ```

4. **Application Layer Deployment:**
   ```bash
   docker compose up backend -d
   docker logs loni-backend --tail 20  # Verify startup
   docker compose up frontend -d
   ```

5. **Monitoring Stack Deployment:**
   ```bash
   docker compose up prometheus grafana -d
   docker compose up cadvisor postgres-exporter redis-exporter -d
   ```

6. **Infrastructure Services:**
   ```bash
   docker compose up nginx jaeger fluent-bit n8n -d
   ```

7. **Verification:**
   ```bash
   docker compose ps  # All services should be running/healthy
   curl http://localhost:8000/health  # Backend health
   curl http://localhost:3000/api/health  # Frontend health
   curl http://localhost:9090/-/healthy  # Prometheus health
   ```

### 6.2 Service Restart and Recovery

**Individual Service Restart:**
```bash
docker restart loni-[service-name]
docker logs loni-[service-name] --tail 20
```

**Complete Stack Restart:**
```bash
docker compose down
docker compose up -d
```

**Recovery from Failed State:**
```bash
docker compose down
docker system prune -f  # Remove unused containers/networks
docker compose up -d
```

### 6.3 Log Analysis and Debugging

**Centralized Log Analysis:**
```bash
# View all service logs
docker compose logs --tail=50

# Service-specific logs
docker logs loni-backend --tail=50 --follow

# Error pattern search
docker logs loni-prometheus 2>&1 | grep -i error

# Log aggregation via Fluent Bit
tail -f ../../data/logs/containers/*.log
```

**Debug Workflow:**
1. Check service status: `docker compose ps`
2. Examine logs: `docker logs [container] --tail 50`
3. Verify configuration: `docker exec [container] cat /path/to/config`
4. Test connectivity: `docker exec [container] curl http://dependency:port/health`
5. Check environment: `docker exec [container] env | grep [VAR]`

---

## 7. AI Agent Guidelines

### 7.1 Systematic Problem Resolution Approach

**Phase 1: Comprehensive Log Analysis**
1. Retrieve complete logs: `docker logs [container] --tail 50`
2. Identify error patterns and timestamps
3. Document exact error messages and stack traces

**Phase 2: Root Cause Analysis**
1. Analyze configuration files for syntax errors
2. Verify environment variable availability
3. Check service dependencies and network connectivity
4. Identify circular dependencies or startup order issues

**Phase 3: Resolution Planning**
1. Prioritize fixes based on dependency relationships
2. Plan configuration file modifications
3. Identify required environment variable updates
4. Determine service restart sequence

**Phase 4: Implementation**
1. Make all configuration changes before restarting containers
2. Validate changes with `docker compose config`
3. Restart services in dependency order
4. Verify resolution with logs and health checks

### 7.2 Configuration File Editing Best Practices

**YAML Configuration:**
- Always validate syntax before deployment
- Use consistent indentation (2 spaces)
- Quote string values containing special characters
- Comment out unused sections rather than deleting

**Environment Variables:**
- Use descriptive variable names
- Group related variables together
- Document required vs optional variables
- Use secure random values for passwords

**Docker Compose:**
- Maintain consistent service naming
- Use explicit dependency conditions
- Implement proper health checks
- Document port mappings and volume mounts

### 7.3 Error Pattern Recognition

**Common Error Patterns:**

1. **Configuration Syntax Errors:**
   - Pattern: `yaml: unmarshal errors`
   - Action: Validate YAML syntax and field names

2. **Service Resolution Failures:**
   - Pattern: `host not found in upstream`
   - Action: Implement dynamic resolution or fix dependencies

3. **Authentication Failures:**
   - Pattern: `password authentication failed`
   - Action: Verify environment variables and credential configuration

4. **Import Errors:**
   - Pattern: `ImportError: cannot import name`
   - Action: Check dependency versions and update imports

5. **Health Check Failures:**
   - Pattern: `unhealthy` status in `docker compose ps`
   - Action: Verify health check endpoints and adjust timeouts

### 7.4 Automated Resolution Strategies

**Configuration Validation Script:**
```bash
#!/bin/bash
# Validate all configurations before deployment
docker compose config > /dev/null && echo "✅ Docker Compose valid" || echo "❌ Docker Compose invalid"
yamllint config/prometheus/prometheus.yml && echo "✅ Prometheus config valid" || echo "❌ Prometheus config invalid"
nginx -t -c config/nginx/nginx.conf && echo "✅ Nginx config valid" || echo "❌ Nginx config invalid"
```

**Health Check Monitoring:**
```bash
#!/bin/bash
# Monitor service health and restart unhealthy services
for service in $(docker compose ps --services); do
    status=$(docker compose ps $service --format "{{.Health}}")
    if [[ "$status" == "unhealthy" ]]; then
        echo "Restarting unhealthy service: $service"
        docker compose restart $service
    fi
done
```

---

## Conclusion

This documentation provides a comprehensive reference for AI coding agents working with the LONI platform's Docker infrastructure. By following these guidelines and procedures, agents can effectively deploy, troubleshoot, and maintain the containerized environment while avoiding the common pitfalls documented in our systematic resolution process.

**Key Success Factors:**
1. Systematic approach to problem resolution
2. Proper configuration management and validation
3. Understanding of service dependencies and startup sequences
4. Implementation of robust health checks and monitoring
5. Platform-specific considerations (Windows compatibility)

For additional support or updates to this documentation, refer to the infrastructure team or update this file with new findings and resolutions.

# PRP: LONI - Modern AI Application Platform

## FEATURE

### Core Functionality
Create a sophisticated AI application platform that provides a modern, intuitive interface for interacting with AI models, managing data, and orchestrating complex AI workflows. The platform should be built with cutting-edge technologies while maintaining clean architecture and SOLID principles.

### Key Requirements
1. **Frontend Application**: Next.js 14 with Turbopack, Bun package manager, modern UI stack
2. **Backend API**: FastAPI with MCP protocol, RAG implementation, AI agent orchestration
3. **Infrastructure**: Containerized services with monitoring and observability
4. **Data Management**: Intelligent model management with Ollama integration
5. **Configuration**: Centralized environment and service configuration
6. **SOLID Architecture**: Single Responsibility Principle with one class per file
7. **API-First Design**: Frontend purely consumes backend APIs without business logic

### Specific Technology Stack

#### Frontend (apps/frontend)
- **Next.js 14** with Turbopack for ultra-fast development builds
- **Bun** as package manager for superior performance
- **AG-UI Protocol** for advanced data grid and table components
- **CopilotKit** for AI-powered UI interactions and assistance
- **ShadCN UI** component library for consistent design system
- **TailwindCSS** with global styles and design tokens
- **TypeScript** for type safety and developer experience
- **Docker** containerization for deployment consistency

#### Backend (apps/backend)
- **FastAPI** with async/await patterns for high-performance APIs
- **MCP (Model Context Protocol)** for standardized AI model communication
- **RAG Pipeline** with Qdrant vector database for semantic search
- **Embedding Models** for document and query vectorization
- **Pydantic-AI Agent** for structured AI interactions with validation
- **LangGraph Agent** for complex multi-step workflow orchestration
- **UV** as Python package manager for fast dependency resolution
- **SQLAlchemy** with async support for database operations
- **Docker** containerization with health checks and monitoring

#### Infrastructure (apps/infra)
- **PostgreSQL 15** for relational data storage with full-text search
- **Qdrant** vector database for embeddings and similarity search
- **Ollama** for local LLM serving with model lifecycle management
- **Neo4j** graph database for knowledge graphs and relationships
- **n8n** workflow automation platform for integration orchestration
- **Grafana** for comprehensive monitoring dashboards and alerting
- **Prometheus** for metrics collection and time-series data
- **Redis** for caching and session management
- **Docker Compose** for service orchestration and networking

### Acceptance Criteria
- [ ] Modular architecture with clear separation of concerns
- [ ] One class per file following Single Responsibility Principle
- [ ] Frontend consumes backend APIs exclusively (no business logic)
- [ ] Complete MCP protocol implementation for AI model communication
- [ ] RAG pipeline with semantic search and context retrieval
- [ ] Ollama integration with model discovery and management
- [ ] Containerized deployment with health checks and monitoring
- [ ] Comprehensive configuration management across environments
- [ ] Real-time model performance metrics and usage tracking
- [ ] Intuitive UI for complex AI interactions and workflows

## EXAMPLES

### Similar Implementation Patterns from Codebase

#### 1. FastAPI Application Structure (ai-agent-mastery/backend)
```python
# Reference: ai-agent-mastery/6_Agent_Deployment/backend_agent_api/agent_api.py
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import asyncio

app = FastAPI(title="LONI AI Platform API")

# CORS middleware for frontend communication
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[str] = None

@app.post("/api/chat")
async def chat_endpoint(request: ChatRequest):
    """Handle chat interactions with AI agents."""
    pass
```

#### 2. Next.js Application Structure (ai-agent-mastery/frontend)
```typescript
// Reference: ai-agent-mastery/5_Agent_Application/frontend/src/components/chat/
import { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface ChatComponentProps {
  onMessageSent: (message: string) => void;
}

export function ChatComponent({ onMessageSent }: ChatComponentProps) {
  const [message, setMessage] = useState('');
  
  const sendMessage = useMutation({
    mutationFn: async (msg: string) => {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: msg })
      });
      return response.json();
    },
    onSuccess: () => onMessageSent(message)
  });
  
  return (
    <div className="flex gap-2">
      <Input 
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        placeholder="Ask me anything..."
      />
      <Button onClick={() => sendMessage.mutate(message)}>
        Send
      </Button>
    </div>
  );
}
```

#### 3. Docker Compose Service Definition (local-ai-packaged)
```yaml
# Reference: local-ai-packaged/docker-compose.yml
version: '3.8'
services:
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__LOG_LEVEL=INFO
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  qdrant_data:
  ollama_data:
```

#### 4. Pydantic AI Agent Implementation (ai-agent-mastery)
```python
# Reference: ai-agent-mastery/4_Pydantic_AI_Agent/agent.py
from pydantic_ai import Agent, RunContext
from pydantic import BaseModel
from typing import List, Optional
import asyncio

class AgentDeps(BaseModel):
    """Dependency injection for agent services."""
    database_client: Any
    embedding_client: Any
    vector_store: Any

class QueryResult(BaseModel):
    """Structured response from agent."""
    response: str
    sources: List[str]
    confidence: float

agent = Agent(
    'gpt-4',
    deps_type=AgentDeps,
    result_type=QueryResult,
    system_prompt="You are an intelligent AI assistant with access to knowledge retrieval."
)

@agent.tool
async def search_knowledge(ctx: RunContext[AgentDeps], query: str) -> str:
    """Search the knowledge base for relevant information."""
    # Embed query
    query_embedding = await ctx.deps.embedding_client.embed(query)
    
    # Search vector store
    results = await ctx.deps.vector_store.search(
        vector=query_embedding,
        limit=5,
        score_threshold=0.7
    )
    
    return f"Found {len(results)} relevant documents"
```

#### 5. Environment Configuration Management
```python
# Reference pattern for configuration management
from pydantic_settings import BaseSettings
from typing import Optional

class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    host: str = "localhost"
    port: int = 5432
    database: str = "loni"
    username: str = "postgres"
    password: str
    
    class Config:
        env_prefix = "DATABASE_"

class OllamaSettings(BaseSettings):
    """Ollama service configuration."""
    host: str = "localhost"
    port: int = 11434
    models_path: str = "/data/models"
    
    class Config:
        env_prefix = "OLLAMA_"

class AppSettings(BaseSettings):
    """Main application settings."""
    debug: bool = False
    cors_origins: List[str] = ["http://localhost:3000"]
    secret_key: str
    
    database: DatabaseSettings = DatabaseSettings()
    ollama: OllamaSettings = OllamaSettings()
```

## DOCUMENTATION

### Architecture References

#### 1. MCP (Model Context Protocol) Specification
- **URL**: https://spec.modelcontextprotocol.io/
- **Key Components**: Server/Client communication, Resource management, Tool calling
- **Implementation**: Standardized interface for AI model interactions

#### 2. Next.js 14 with Turbopack
- **URL**: https://nextjs.org/docs/app/api-reference/next-config-js/turbo
- **Features**: Ultra-fast bundling, Server Components, App Router
- **Performance**: 700x faster than Webpack for large applications

#### 3. Pydantic-AI Framework
- **URL**: https://ai.pydantic.dev/
- **Features**: Type-safe AI agents, Structured outputs, Tool calling
- **Integration**: Seamless with FastAPI and async patterns

#### 4. LangGraph Agent Framework
- **URL**: https://langchain-ai.github.io/langgraph/
- **Features**: Multi-agent workflows, State management, Tool orchestration
- **Use Cases**: Complex reasoning chains, Human-in-the-loop workflows

#### 5. CopilotKit Integration
- **URL**: https://copilotkit.ai/
- **Features**: AI-powered components, Chat interfaces, Action triggers
- **Integration**: React hooks and components for AI interactions

#### 6. AG-UI Protocol Implementation
- **URL**: https://ag-grid.com/
- **Features**: Advanced data grids, Virtual scrolling, Enterprise features
- **Performance**: Handle millions of rows with smooth interactions

### Database Schema Design

#### PostgreSQL Schema
```sql
-- Core application tables
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI model management
CREATE TABLE ai_models (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    provider VARCHAR(100) NOT NULL,
    model_type VARCHAR(100) NOT NULL, -- 'chat', 'embedding', 'vision'
    capabilities JSONB DEFAULT '{}',
    status VARCHAR(50) DEFAULT 'available',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Document storage for RAG
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    content_type VARCHAR(100),
    source_url VARCHAR(500),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Full-text search index
CREATE INDEX idx_documents_content ON documents USING GIN(to_tsvector('english', content));
```

#### Qdrant Vector Store Schema
```python
from qdrant_client.models import VectorParams, Distance

# Collection configuration
collection_config = {
    "vectors": VectorParams(
        size=1536,  # OpenAI embedding dimension
        distance=Distance.COSINE
    ),
    "payload_schema": {
        "document_id": "keyword",
        "user_id": "keyword", 
        "title": "text",
        "content": "text",
        "source": "keyword",
        "timestamp": "datetime"
    }
}
```

### API Design Patterns

#### RESTful API Structure
```python
# User management endpoints
@router.get("/users/me", response_model=UserResponse)
@router.put("/users/me", response_model=UserResponse)

# Conversation management
@router.get("/conversations", response_model=List[ConversationResponse])
@router.post("/conversations", response_model=ConversationResponse)
@router.get("/conversations/{conversation_id}", response_model=ConversationDetailResponse)
@router.delete("/conversations/{conversation_id}")

# AI interaction endpoints
@router.post("/chat", response_model=ChatResponse)
@router.post("/chat/stream")  # Server-sent events
@router.post("/documents/upload", response_model=DocumentResponse)
@router.post("/documents/search", response_model=SearchResponse)

# Model management
@router.get("/models", response_model=List[ModelResponse])
@router.post("/models/{model_id}/download")
@router.get("/models/{model_id}/status")
```

## OTHER CONSIDERATIONS

### 1. SOLID Architecture Implementation

#### Single Responsibility Principle
```python
# Each file contains exactly one class with one responsibility

# services/chat_service.py
class ChatService:
    """Handles chat message processing and response generation."""
    
    def __init__(self, agent: Agent, conversation_repo: ConversationRepository):
        self.agent = agent
        self.conversation_repo = conversation_repo
    
    async def process_message(self, message: str, conversation_id: str) -> ChatResponse:
        """Process a chat message and return AI response."""
        pass

# services/document_service.py  
class DocumentService:
    """Handles document upload, processing, and retrieval."""
    
    def __init__(self, vector_store: VectorStore, embedder: EmbeddingService):
        self.vector_store = vector_store
        self.embedder = embedder
    
    async def upload_document(self, content: str, metadata: dict) -> DocumentResponse:
        """Upload and vectorize a document."""
        pass

# services/model_service.py
class ModelService:
    """Manages AI model lifecycle and availability."""
    
    def __init__(self, ollama_client: OllamaClient, model_repo: ModelRepository):
        self.ollama_client = ollama_client
        self.model_repo = model_repo
    
    async def list_available_models(self) -> List[ModelInfo]:
        """List all available AI models with metadata."""
        pass
```

#### Open/Closed Principle
```python
# Abstract base classes for extensibility
from abc import ABC, abstractmethod

class EmbeddingService(ABC):
    """Abstract embedding service interface."""
    
    @abstractmethod
    async def embed_text(self, text: str) -> List[float]:
        """Generate embeddings for text."""
        pass
    
    @abstractmethod
    async def embed_batch(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts."""
        pass

class OpenAIEmbeddingService(EmbeddingService):
    """OpenAI embedding implementation."""
    pass

class OllamaEmbeddingService(EmbeddingService):
    """Ollama embedding implementation."""
    pass
```

### 2. MCP Protocol Implementation

#### Server Implementation
```python
from mcp import Server, Tool, Resource
from mcp.types import TextContent, EmbeddedResource

class LoniMCPServer(Server):
    """LONI MCP server for AI model communication."""
    
    def __init__(self):
        super().__init__(name="loni-server", version="1.0.0")
        self._setup_tools()
        self._setup_resources()
    
    def _setup_tools(self):
        """Register available tools."""
        
        @self.tool("search_documents")
        async def search_documents(query: str, limit: int = 5) -> List[dict]:
            """Search documents using semantic similarity."""
            # Implement vector search
            pass
        
        @self.tool("summarize_conversation")
        async def summarize_conversation(conversation_id: str) -> str:
            """Summarize a conversation thread."""
            # Implement conversation summarization
            pass
    
    def _setup_resources(self):
        """Register available resources."""
        
        @self.resource("user_profile")
        async def get_user_profile(user_id: str) -> EmbeddedResource:
            """Get user profile information."""
            # Return user profile data
            pass
```

#### Client Integration
```python
from mcp import Client
import asyncio

class LoniMCPClient:
    """Client for communicating with MCP servers."""
    
    def __init__(self, server_url: str):
        self.client = Client(server_url)
    
    async def search_knowledge(self, query: str) -> List[dict]:
        """Search knowledge base via MCP."""
        return await self.client.call_tool("search_documents", {"query": query})
    
    async def get_user_context(self, user_id: str) -> dict:
        """Get user context via MCP."""
        return await self.client.get_resource("user_profile", {"user_id": user_id})
```

### 3. RAG Pipeline Architecture

#### Document Processing Pipeline
```python
class DocumentProcessor:
    """Processes documents for RAG retrieval."""
    
    def __init__(self, chunker: TextChunker, embedder: EmbeddingService, vector_store: VectorStore):
        self.chunker = chunker
        self.embedder = embedder
        self.vector_store = vector_store
    
    async def process_document(self, document: Document) -> ProcessingResult:
        """Process document through RAG pipeline."""
        
        # 1. Extract and clean text
        cleaned_text = await self._clean_text(document.content)
        
        # 2. Chunk text into segments
        chunks = await self.chunker.chunk_text(cleaned_text, chunk_size=512, overlap=50)
        
        # 3. Generate embeddings
        embeddings = await self.embedder.embed_batch([chunk.text for chunk in chunks])
        
        # 4. Store in vector database
        points = [
            {
                "id": chunk.id,
                "vector": embedding,
                "payload": {
                    "document_id": document.id,
                    "text": chunk.text,
                    "metadata": chunk.metadata
                }
            }
            for chunk, embedding in zip(chunks, embeddings)
        ]
        
        await self.vector_store.upsert_points(points)
        
        return ProcessingResult(
            document_id=document.id,
            chunks_processed=len(chunks),
            embeddings_generated=len(embeddings)
        )
```

#### Retrieval and Generation
```python
class RAGService:
    """Retrieval-Augmented Generation service."""
    
    def __init__(self, vector_store: VectorStore, embedder: EmbeddingService, llm: Agent):
        self.vector_store = vector_store
        self.embedder = embedder
        self.llm = llm
    
    async def query(self, question: str, user_id: str, top_k: int = 5) -> RAGResponse:
        """Answer question using RAG pipeline."""
        
        # 1. Embed the question
        question_embedding = await self.embedder.embed_text(question)
        
        # 2. Retrieve relevant documents
        search_results = await self.vector_store.search(
            vector=question_embedding,
            filter={"user_id": user_id},
            limit=top_k,
            score_threshold=0.7
        )
        
        # 3. Prepare context
        context = "\n\n".join([result.payload["text"] for result in search_results])
        
        # 4. Generate response
        prompt = f"""
        Context: {context}
        
        Question: {question}
        
        Answer the question based on the provided context. If the context doesn't contain enough information, say so.
        """
        
        response = await self.llm.run(prompt)
        
        return RAGResponse(
            answer=response.response,
            sources=[result.payload["document_id"] for result in search_results],
            confidence=response.confidence
        )
```

### 4. Ollama Integration and Model Management

#### Model Discovery Service
```python
class OllamaModelService:
    """Manages Ollama models with metadata and lifecycle."""
    
    def __init__(self, ollama_client: OllamaClient, models_path: Path):
        self.ollama_client = ollama_client
        self.models_path = models_path
        self.model_metadata = self._load_model_metadata()
    
    def _load_model_metadata(self) -> dict:
        """Load model metadata from configuration."""
        return {
            "llama3.2:latest": {
                "type": "chat",
                "capabilities": ["text_generation", "conversation"],
                "context_length": 128000,
                "parameter_count": "8B",
                "vision_support": False,
                "embedding_support": False
            },
            "llama3.2-vision:latest": {
                "type": "vision",
                "capabilities": ["text_generation", "image_analysis", "conversation"],
                "context_length": 128000,
                "parameter_count": "11B", 
                "vision_support": True,
                "embedding_support": False
            },
            "nomic-embed-text:latest": {
                "type": "embedding",
                "capabilities": ["text_embedding", "semantic_search"],
                "dimension": 768,
                "max_sequence_length": 8192,
                "vision_support": False,
                "embedding_support": True
            }
        }
    
    async def list_available_models(self) -> List[ModelInfo]:
        """List models available for download."""
        available_models = []
        
        for model_name, metadata in self.model_metadata.items():
            # Check if model is downloaded
            is_downloaded = await self._is_model_downloaded(model_name)
            
            # Get model size if downloaded
            size = await self._get_model_size(model_name) if is_downloaded else None
            
            available_models.append(ModelInfo(
                name=model_name,
                type=metadata["type"],
                capabilities=metadata["capabilities"],
                is_downloaded=is_downloaded,
                size_gb=size,
                metadata=metadata
            ))
        
        return available_models
    
    async def download_model(self, model_name: str) -> AsyncIterator[DownloadProgress]:
        """Download model with progress tracking."""
        async for progress in self.ollama_client.pull(model_name, stream=True):
            yield DownloadProgress(
                model_name=model_name,
                status=progress.get("status", "downloading"),
                completed=progress.get("completed", 0),
                total=progress.get("total", 0),
                percent=progress.get("completed", 0) / progress.get("total", 1) * 100
            )
```

### 5. Frontend Architecture with CopilotKit

#### AI-Powered Chat Component
```typescript
// components/chat/ai-chat.tsx
import { CopilotChat, CopilotKit } from "@copilotkit/react-core";
import { CopilotSidebar } from "@copilotkit/react-ui";
import { useCopilotReadable, useCopilotAction } from "@copilotkit/react-core";

export function AIChatInterface() {
  // Make application state readable to AI
  useCopilotReadable({
    description: "Current user conversation history",
    value: conversationHistory
  });

  // Register AI actions
  useCopilotAction({
    name: "searchDocuments",
    description: "Search user's documents for relevant information",
    parameters: [
      {
        name: "query",
        type: "string",
        description: "Search query for documents"
      }
    ],
    handler: async ({ query }) => {
      const results = await fetch("/api/documents/search", {
        method: "POST",
        body: JSON.stringify({ query })
      });
      return await results.json();
    }
  });

  return (
    <CopilotKit publicApiKey={process.env.NEXT_PUBLIC_COPILOT_API_KEY}>
      <div className="flex h-screen">
        <div className="flex-1">
          <CopilotChat
            instructions="You are an AI assistant with access to the user's documents and conversation history."
            placeholder="Ask me anything about your data..."
          />
        </div>
        <CopilotSidebar>
          <DocumentSearchPanel />
          <ModelSelectionPanel />
        </CopilotSidebar>
      </div>
    </CopilotKit>
  );
}
```

### 6. Performance and Monitoring

#### Application Metrics
```python
from prometheus_client import Counter, Histogram, Gauge
import time

# Define metrics
chat_requests_total = Counter('chat_requests_total', 'Total chat requests', ['model', 'status'])
response_time_seconds = Histogram('response_time_seconds', 'Response time for chat requests')
active_models = Gauge('active_models_count', 'Number of active AI models')
vector_search_duration = Histogram('vector_search_duration_seconds', 'Time spent on vector searches')

class MetricsMiddleware:
    """Middleware for collecting application metrics."""
    
    async def __call__(self, request: Request, call_next):
        start_time = time.time()
        
        response = await call_next(request)
        
        # Record response time
        duration = time.time() - start_time
        response_time_seconds.observe(duration)
        
        # Record request count
        if request.url.path.startswith("/api/chat"):
            chat_requests_total.labels(
                model=request.headers.get("X-Model", "unknown"),
                status=response.status_code
            ).inc()
        
        return response
```

### 7. Security and Authentication

#### JWT Authentication
```python
from fastapi_users import FastAPIUsers, BaseUserManager
from fastapi_users.authentication import JWTAuthentication
from fastapi_users.db import SQLAlchemyUserDatabase

class UserManager(BaseUserManager[User, UUID]):
    """User management with security features."""
    reset_password_token_secret = SECRET
    verification_token_secret = SECRET

    async def on_after_register(self, user: User, request: Optional[Request] = None):
        """Actions after user registration."""
        # Send welcome email, initialize user data
        pass

    async def on_after_login(self, user: User, request: Optional[Request] = None):
        """Actions after user login."""
        # Update last login time, log activity
        pass

# JWT authentication configuration
jwt_authentication = JWTAuthentication(
    secret=SECRET,
    lifetime_seconds=3600,
    tokenUrl="auth/jwt/login",
)

fastapi_users = FastAPIUsers[User, UUID](
    get_user_manager,
    [jwt_authentication],
)
```

### 8. Error Handling and Resilience

#### Comprehensive Error Handling
```python
from fastapi import HTTPException
import logging

class LoniError(Exception):
    """Base exception for LONI platform."""
    
    def __init__(self, message: str, error_code: str, details: dict = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)

class ModelNotAvailableError(LoniError):
    """Raised when requested AI model is not available."""
    pass

class VectorSearchError(LoniError):
    """Raised when vector search fails."""
    pass

@app.exception_handler(LoniError)
async def loni_exception_handler(request: Request, exc: LoniError):
    """Handle LONI-specific exceptions."""
    logging.error(f"LONI Error: {exc.error_code} - {exc.message}", extra=exc.details)
    
    return JSONResponse(
        status_code=400,
        content={
            "error": exc.error_code,
            "message": exc.message,
            "details": exc.details
        }
    )
```

This comprehensive PRP provides complete context for implementing a production-ready AI platform following SOLID principles and modern development practices. The implementation will create a sophisticated system with intelligent model management, advanced UI interactions, robust backend services, and comprehensive monitoring.
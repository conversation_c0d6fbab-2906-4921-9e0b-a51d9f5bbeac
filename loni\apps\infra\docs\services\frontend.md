# Frontend Application Service Documentation

## Service Overview

The Frontend service provides the user interface for the LONI platform, built with Next.js 14, React, and TypeScript. It serves as the primary interaction point for users, offering a modern, responsive web application with real-time features and AI-powered interfaces.

### Role in LONI Platform
- **User Interface**: Primary web application interface for all user interactions
- **AI Chat Interface**: Real-time chat interface for AI conversations
- **Dashboard**: User dashboard for managing conversations, settings, and data
- **Authentication UI**: Login, registration, and user management interfaces
- **Real-time Features**: WebSocket connections for live updates and notifications
- **API Integration**: Frontend client for backend API consumption

## Docker Configuration

```yaml
frontend:
  build:
    context: ../frontend
    dockerfile: Dockerfile
    target: production
    cache_from:
      - loni-frontend:latest
  image: loni-frontend:latest
  container_name: loni-frontend
  environment:
    NODE_ENV: production
    NEXT_PUBLIC_API_URL: http://backend:8000
    NEXT_PUBLIC_WS_URL: ws://backend:8000/ws
    NEXT_PUBLIC_APP_NAME: LONI
  volumes:
    - ../../data/logs/applications:/app/logs
  ports:
    - "3000:3000"
  depends_on:
    backend:
      condition: service_healthy
      restart: true
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
    interval: 30s
    timeout: 10s
    retries: 3
  restart: unless-stopped
  networks:
    - loni-network
  logging:
    driver: "json-file"
    options:
      max-size: "10m"
      max-file: "3"
```

## Environment Variables

### Build-time Variables (NEXT_PUBLIC_*)
| Variable | Description | Default | Exposure |
|----------|-------------|---------|----------|
| `NEXT_PUBLIC_API_URL` | Backend API base URL | `http://backend:8000` | **Client-side** |
| `NEXT_PUBLIC_WS_URL` | WebSocket connection URL | `ws://backend:8000/ws` | **Client-side** |
| `NEXT_PUBLIC_APP_NAME` | Application display name | `LONI` | **Client-side** |
| `NEXT_PUBLIC_VERSION` | Application version | From package.json | **Client-side** |

### Runtime Variables
| Variable | Description | Default | Security Level |
|----------|-------------|---------|----------------|
| `NODE_ENV` | Node.js environment | `production` | Standard |
| `PORT` | Application port | `3000` | Standard |
| `HOSTNAME` | Bind hostname | `0.0.0.0` | Standard |

### Optional Configuration Variables
| Variable | Description | Default | Use Case |
|----------|-------------|---------|----------|
| `NEXT_PUBLIC_ANALYTICS_ID` | Analytics tracking ID | None | User analytics |
| `NEXT_PUBLIC_SENTRY_DSN` | Error tracking DSN | None | Error monitoring |
| `NEXT_PUBLIC_FEATURE_FLAGS` | Feature flag configuration | `{}` | Feature toggles |
| `NEXT_PUBLIC_THEME` | Default UI theme | `light` | UI customization |

## Volume Mounts

### Application Logs
- **Log Directory**: `../../data/logs/applications:/app/logs`
  - Contains application logs, error logs, and access logs
  - **Retention**: 7 days, rotated daily
  - **Format**: Structured JSON logging

### Static Assets (Production)
- **Built Assets**: Included in Docker image during build
  - Optimized and minified JavaScript bundles
  - CSS stylesheets and static assets
  - **Caching**: Aggressive caching with content hashing

## Network Configuration

### Port Mappings
- **Primary Port**: `3000:3000` (Next.js development server)
- **External Access**: Direct access for development, proxied via Nginx in production
- **Internal Access**: Available on `loni-network` as `frontend:3000`

### API Communication
- **Backend API**: HTTP requests to `backend:8000`
- **WebSocket**: Real-time connection to `backend:8000/ws`
- **Static Assets**: Served directly by Next.js server
- **Proxy Configuration**: Nginx reverse proxy in production

## Health Checks

### Primary Health Check
```bash
curl -f http://localhost:3000/api/health
```
- **Interval**: 30 seconds
- **Timeout**: 10 seconds
- **Retries**: 3 attempts
- **Endpoint**: Custom health check API route

### Advanced Health Monitoring
```bash
# Check application status
curl http://localhost:3000/api/health

# Verify static asset serving
curl -I http://localhost:3000/_next/static/css/app.css

# Test API connectivity
curl http://localhost:3000/api/status

# Monitor WebSocket connectivity
wscat -c ws://localhost:3000/api/ws
```

## Security Configuration

### Content Security Policy
```javascript
// next.config.js
const securityHeaders = [
  {
    key: 'Content-Security-Policy',
    value: `
      default-src 'self';
      script-src 'self' 'unsafe-eval' 'unsafe-inline';
      style-src 'self' 'unsafe-inline';
      img-src 'self' data: https:;
      connect-src 'self' ws: wss:;
    `.replace(/\s{2,}/g, ' ').trim()
  }
]
```

### Environment Variable Security
```bash
# Secure environment variables (server-side only)
API_SECRET_KEY=server_only_secret
DATABASE_URL=server_only_connection

# Public environment variables (client-side accessible)
NEXT_PUBLIC_API_URL=http://backend:8000
NEXT_PUBLIC_APP_NAME=LONI
```

### Authentication Integration
```typescript
// Authentication with backend
const authConfig = {
  apiUrl: process.env.NEXT_PUBLIC_API_URL,
  tokenStorage: 'httpOnly', // Secure cookie storage
  refreshTokenRotation: true,
  sessionTimeout: 3600000 // 1 hour
}
```

## Performance Optimization

### Build Optimization
```javascript
// next.config.js
module.exports = {
  output: 'standalone',
  compress: true,
  poweredByHeader: false,
  generateEtags: false,
  
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['@mui/material', 'lodash']
  },
  
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        fs: false,
        net: false,
        tls: false
      }
    }
    return config
  }
}
```

### Runtime Performance
```typescript
// Performance monitoring
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

function sendToAnalytics(metric) {
  // Send performance metrics to monitoring service
  fetch('/api/analytics', {
    method: 'POST',
    body: JSON.stringify(metric)
  })
}

getCLS(sendToAnalytics)
getFID(sendToAnalytics)
getFCP(sendToAnalytics)
getLCP(sendToAnalytics)
getTTFB(sendToAnalytics)
```

### Caching Strategy
```javascript
// Cache configuration
const cacheConfig = {
  'Cache-Control': 'public, max-age=31536000, immutable', // Static assets
  'Cache-Control': 'public, max-age=3600, must-revalidate', // API responses
  'Cache-Control': 'no-cache, no-store, must-revalidate' // Dynamic content
}
```

## Troubleshooting

### Common Issues

#### Container Won't Start
```bash
# Check logs
docker logs loni-frontend

# Common causes:
# 1. Build failures
# 2. Missing environment variables
# 3. Port conflicts
# 4. Node.js version incompatibility
```

#### Build Failures
```bash
# Check build logs
docker build --no-cache -t loni-frontend ../frontend

# Common issues:
# 1. TypeScript compilation errors
# 2. Missing dependencies
# 3. Environment variable issues
# 4. Asset optimization failures
```

#### Runtime Errors
```bash
# Check application logs
docker exec loni-frontend cat /app/logs/application.log

# Check Next.js logs
docker exec loni-frontend cat /app/.next/trace

# Monitor real-time logs
docker logs -f loni-frontend
```

#### API Connection Issues
```bash
# Test backend connectivity
docker exec loni-frontend curl http://backend:8000/health

# Check environment variables
docker exec loni-frontend env | grep NEXT_PUBLIC

# Verify network connectivity
docker exec loni-frontend nslookup backend
```

## Development vs Production

### Development Configuration
```yaml
# docker-compose.dev.yml
frontend:
  build:
    target: development
  environment:
    NODE_ENV: development
    NEXT_PUBLIC_API_URL: http://localhost:8000
  volumes:
    - ../frontend:/app
    - /app/node_modules
  command: npm run dev
```

### Production Configuration
```yaml
# docker-compose.yml
frontend:
  build:
    target: production
  environment:
    NODE_ENV: production
    NEXT_PUBLIC_API_URL: http://backend:8000
  command: npm start
```

## Monitoring and Metrics

### Application Metrics
- **Page Load Times**: Core Web Vitals monitoring
- **API Response Times**: Backend API call performance
- **Error Rates**: JavaScript errors and API failures
- **User Interactions**: Click tracking and user flow analysis
- **Bundle Size**: JavaScript bundle size monitoring

### Performance Monitoring
```typescript
// Custom metrics collection
export function reportWebVitals(metric) {
  switch (metric.name) {
    case 'CLS':
    case 'FID':
    case 'FCP':
    case 'LCP':
    case 'TTFB':
      // Send to analytics service
      analytics.track('Web Vital', {
        name: metric.name,
        value: metric.value,
        id: metric.id
      })
      break
    default:
      break
  }
}
```

### Error Tracking
```typescript
// Error boundary and reporting
import * as Sentry from '@sentry/nextjs'

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 0.1
})
```

## Integration with LONI Services

### Backend API Integration
```typescript
// API client configuration
const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 10000,
  withCredentials: true
})

// Request interceptor for authentication
apiClient.interceptors.request.use((config) => {
  const token = getAuthToken()
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})
```

### WebSocket Integration
```typescript
// Real-time connection
const wsUrl = process.env.NEXT_PUBLIC_WS_URL
const socket = new WebSocket(wsUrl)

socket.onmessage = (event) => {
  const data = JSON.parse(event.data)
  // Handle real-time updates
}
```

### State Management
```typescript
// Global state management with Zustand
import { create } from 'zustand'

interface AppState {
  user: User | null
  conversations: Conversation[]
  isLoading: boolean
  setUser: (user: User) => void
  addConversation: (conversation: Conversation) => void
}

export const useAppStore = create<AppState>((set) => ({
  user: null,
  conversations: [],
  isLoading: false,
  setUser: (user) => set({ user }),
  addConversation: (conversation) => 
    set((state) => ({ 
      conversations: [...state.conversations, conversation] 
    }))
}))
```

## Operational Procedures

### Deployment Process
1. **Build Stage**: Multi-stage Docker build with dependency caching
2. **Test Stage**: Run unit tests and linting
3. **Production Build**: Optimize and minify assets
4. **Container Start**: Health check validation
5. **Service Registration**: Register with load balancer

### Maintenance Tasks
- **Daily**: Monitor performance metrics and error rates
- **Weekly**: Update dependencies and security patches
- **Monthly**: Performance optimization and bundle analysis
- **Quarterly**: Major version updates and architecture review

### Scaling Considerations
- **Horizontal Scaling**: Multiple frontend instances behind load balancer
- **CDN Integration**: Static asset delivery via CDN
- **Edge Deployment**: Deploy to edge locations for reduced latency
- **Progressive Web App**: Offline capabilities and caching strategies

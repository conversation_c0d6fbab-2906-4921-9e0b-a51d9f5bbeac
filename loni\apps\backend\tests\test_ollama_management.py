"""
Tests for Ollama model management system.

This module contains unit tests for the Ollama model discovery,
management, and API functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from integrations.ollama.discovery import ModelDiscoveryService, DiscoveredModel
from integrations.ollama.management import ModelManagementService
from integrations.ollama.models import ModelDownloadProgress
from api.schemas.ollama_schemas import ModelInstallRequest, AvailableModelResponse


class TestModelDiscoveryService:
    """Test cases for model discovery service."""
    
    @pytest.fixture
    def discovery_service(self):
        """Create a discovery service instance."""
        return ModelDiscoveryService()
    
    @pytest.fixture
    def mock_html_response(self):
        """Mock HTML response from Ollama registry."""
        return """
        <html>
            <body>
                <div class="model-card">
                    <h3 class="model-title">llama3.2</h3>
                    <p class="model-description">Meta's Llama 3.2 model for conversation</p>
                    <span class="tag">7B</span>
                    <span class="tag">chat</span>
                    <a href="/library/llama3.2">View Model</a>
                </div>
                <div class="model-card">
                    <h3 class="model-title">mistral</h3>
                    <p class="model-description">Mistral 7B model</p>
                    <span class="tag">7B</span>
                    <span class="tag">instruct</span>
                    <a href="/library/mistral">View Model</a>
                </div>
            </body>
        </html>
        """
    
    @pytest.mark.asyncio
    async def test_discover_models_success(self, discovery_service, mock_html_response):
        """Test successful model discovery."""
        with patch('httpx.AsyncClient.get') as mock_get:
            # Mock HTTP response
            mock_response = Mock()
            mock_response.text = mock_html_response
            mock_response.raise_for_status = Mock()
            mock_get.return_value = mock_response
            
            # Discover models
            models = await discovery_service.discover_models()
            
            # Verify results
            assert len(models) >= 0  # May return fallback models
            mock_get.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_discover_models_with_cache(self, discovery_service):
        """Test model discovery with caching."""
        # Mock cached data
        test_model = DiscoveredModel(
            name="test_model",
            full_identifier="test_model:latest",
            description="Test model",
            use_case="Testing",
            registry_url="https://ollama.com/library/test_model"
        )
        
        # Set cache
        cache_key = "all:all"
        discovery_service._cache[cache_key] = (datetime.utcnow(), [test_model])
        
        # Discover models (should use cache)
        models = await discovery_service.discover_models()
        
        # Verify cached result
        assert len(models) == 1
        assert models[0].name == "test_model"
    
    def test_infer_capabilities(self, discovery_service):
        """Test capability inference from description and tags."""
        # Test code generation
        capabilities = discovery_service._infer_capabilities(
            "A model for code generation and programming",
            ["code", "programming"]
        )
        assert "code_generation" in capabilities
        
        # Test conversation
        capabilities = discovery_service._infer_capabilities(
            "Chat model for conversation",
            ["chat", "dialogue"]
        )
        assert "conversation" in capabilities
        
        # Test embedding
        capabilities = discovery_service._infer_capabilities(
            "Text embedding model",
            ["embed", "vector"]
        )
        assert "text_embedding" in capabilities
    
    def test_estimate_download_size(self, discovery_service):
        """Test download size estimation."""
        # Test known sizes
        size = discovery_service._estimate_download_size(["7B"], "")
        assert size == 4.0
        
        size = discovery_service._estimate_download_size(["13B"], "")
        assert size == 7.5
        
        # Test unknown size
        size = discovery_service._estimate_download_size([], "")
        assert size == 4.0  # Default


class TestModelManagementService:
    """Test cases for model management service."""
    
    @pytest.fixture
    def management_service(self):
        """Create a management service instance."""
        with patch('integrations.ollama.management.get_database_session'):
            return ModelManagementService()
    
    @pytest.fixture
    def mock_discovered_models(self):
        """Mock discovered models."""
        return [
            DiscoveredModel(
                name="llama3.2",
                full_identifier="llama3.2:latest",
                description="Meta's Llama 3.2 model",
                use_case="Conversational AI",
                sizes=["8B"],
                capabilities=["text_generation", "conversation"],
                download_size_gb=4.5,
                tags=["meta", "llama"],
                categories=["Meta", "Language Model"],
                registry_url="https://ollama.com/library/llama3.2"
            )
        ]
    
    @pytest.mark.asyncio
    async def test_discover_available_models(self, management_service, mock_discovered_models):
        """Test discovering available models."""
        with patch.object(management_service.discovery_service, 'discover_models') as mock_discover:
            mock_discover.return_value = mock_discovered_models
            
            with patch.object(management_service, '_update_registry_with_discovered_models') as mock_update:
                mock_update.return_value = None
                
                models = await management_service.discover_available_models()
                
                assert len(models) == 1
                assert models[0].name == "llama3.2"
                mock_discover.assert_called_once()
                mock_update.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_install_model_progress(self, management_service):
        """Test model installation with progress tracking."""
        model_id = "test_model:latest"
        
        # Mock progress updates
        progress_updates = [
            ModelDownloadProgress(
                model_name=model_id,
                status="downloading",
                completed=1024,
                total=2048,
                percent=50.0,
                is_complete=False
            ),
            ModelDownloadProgress(
                model_name=model_id,
                status="complete",
                completed=2048,
                total=2048,
                percent=100.0,
                is_complete=True
            )
        ]
        
        with patch.object(management_service.ollama_client, 'pull_model') as mock_pull:
            mock_pull.return_value = iter(progress_updates)
            
            with patch.object(management_service, '_update_model_status') as mock_update_status:
                with patch.object(management_service, '_verify_installation') as mock_verify:
                    with patch.object(management_service, '_update_installation_metadata') as mock_metadata:
                        
                        # Collect progress updates
                        progress_list = []
                        async for progress in management_service.install_model(model_id):
                            progress_list.append(progress)
                        
                        # Verify progress tracking
                        assert len(progress_list) == 2
                        assert progress_list[0].percent == 50.0
                        assert progress_list[1].percent == 100.0
                        assert progress_list[1].is_complete
                        
                        # Verify method calls
                        mock_pull.assert_called_once()
                        mock_verify.assert_called_once()
                        mock_metadata.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_model_status(self, management_service):
        """Test getting model status."""
        model_id = "test_model:latest"
        
        with patch.object(management_service, 'get_database_session'):
            with patch.object(management_service.ollama_client, 'list_models') as mock_list:
                mock_list.return_value = []
                
                status = await management_service.get_model_status(model_id)
                
                assert status["model_id"] == model_id
                assert "status" in status
                assert "is_installed" in status
    
    @pytest.mark.asyncio
    async def test_uninstall_model(self, management_service):
        """Test model uninstallation."""
        model_id = "test_model:latest"
        
        with patch.object(management_service.ollama_client, 'delete_model') as mock_delete:
            mock_delete.return_value = True
            
            with patch.object(management_service, '_update_model_status') as mock_update_status:
                with patch.object(management_service, '_mark_model_uninstalled') as mock_mark:
                    
                    success = await management_service.uninstall_model(model_id)
                    
                    assert success is True
                    mock_delete.assert_called_once_with(model_id)
                    mock_update_status.assert_called_once()
                    mock_mark.assert_called_once()


class TestOllamaAPI:
    """Test cases for Ollama API endpoints."""
    
    @pytest.fixture
    def mock_service(self):
        """Mock model management service."""
        service = Mock(spec=ModelManagementService)
        service.discover_available_models = AsyncMock()
        service.get_installed_models = AsyncMock()
        service.install_model = AsyncMock()
        service.uninstall_model = AsyncMock()
        service.get_model_status = AsyncMock()
        return service
    
    def test_model_install_request_validation(self):
        """Test model install request validation."""
        # Valid request
        request = ModelInstallRequest(model_id="llama3.2:latest")
        assert request.model_id == "llama3.2:latest"
        assert request.force_reinstall is False
        
        # Test validation
        with pytest.raises(ValueError):
            ModelInstallRequest(model_id="")
        
        with pytest.raises(ValueError):
            ModelInstallRequest(model_id="   ")
    
    def test_available_model_response_schema(self):
        """Test available model response schema."""
        response = AvailableModelResponse(
            name="llama3.2",
            full_identifier="llama3.2:latest",
            description="Meta's Llama 3.2 model",
            use_case="Conversational AI",
            sizes=["8B"],
            capabilities=["text_generation"],
            download_size_gb=4.5,
            tags=["meta"],
            categories=["Language Model"],
            registry_url="https://ollama.com/library/llama3.2",
            is_installed=False
        )
        
        assert response.name == "llama3.2"
        assert response.download_size_gb == 4.5
        assert response.is_installed is False


class TestModelCLI:
    """Test cases for CLI functionality."""
    
    def test_estimate_memory_requirements(self):
        """Test memory requirement estimation."""
        from utils.model_cli import ModelCLI
        
        cli = ModelCLI()
        
        # Test known sizes
        assert cli._estimate_memory_requirements("7B") == 8.0
        assert cli._estimate_memory_requirements("13B") == 16.0
        assert cli._estimate_memory_requirements("70B") == 64.0
        
        # Test unknown size
        assert cli._estimate_memory_requirements("unknown") == 8.0
    
    def test_estimate_size_download(self):
        """Test download size estimation."""
        from utils.model_cli import ModelCLI
        
        cli = ModelCLI()
        
        # Test known sizes
        assert cli._estimate_size_download("7B") == 4.0
        assert cli._estimate_size_download("13B") == 7.5
        assert cli._estimate_size_download("70B") == 40.0
        
        # Test unknown size
        assert cli._estimate_size_download("unknown") == 4.0


@pytest.mark.integration
class TestOllamaIntegration:
    """Integration tests for Ollama system."""
    
    @pytest.mark.asyncio
    async def test_full_workflow(self):
        """Test complete workflow from discovery to installation."""
        # This would be a full integration test
        # Requires actual Ollama service running
        pass
    
    @pytest.mark.asyncio
    async def test_database_integration(self):
        """Test database operations."""
        # This would test actual database operations
        # Requires test database setup
        pass


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])

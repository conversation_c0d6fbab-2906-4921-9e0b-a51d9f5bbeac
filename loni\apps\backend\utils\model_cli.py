"""
Interactive CLI tool for Ollama model selection and management.

This module provides a command-line interface for discovering, selecting,
and installing Ollama models with an interactive user experience.
"""

import asyncio
import sys
from typing import List, Optional, Dict, Tuple
from pathlib import Path

import click
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.text import Text

# Add the backend directory to the path for imports
sys.path.append(str(Path(__file__).parent.parent))

from integrations.ollama.management import ModelManagementService
from integrations.ollama.discovery import DiscoveredModel
from api.schemas.ollama_schemas import ModelSizeOption


class ModelCLI:
    """Interactive CLI for Ollama model management."""
    
    def __init__(self):
        """Initialize the CLI."""
        self.console = Console()
        self.service = ModelManagementService()
    
    async def run_interactive_selection(self) -> None:
        """Run the interactive model selection process."""
        try:
            self.console.print(Panel.fit(
                "[bold blue]LONI Ollama Model Manager[/bold blue]\n"
                "Interactive model discovery and installation tool",
                border_style="blue"
            ))
            
            # Discover available models
            await self._discover_and_display_models()
            
            # Get user selection
            selected_model = await self._get_user_model_selection()
            if not selected_model:
                self.console.print("[yellow]No model selected. Exiting.[/yellow]")
                return
            
            # Handle size selection if multiple sizes available
            selected_size = await self._get_size_selection(selected_model)
            if not selected_size:
                self.console.print("[yellow]No size selected. Exiting.[/yellow]")
                return
            
            # Confirm installation
            if await self._confirm_installation(selected_model, selected_size):
                await self._install_model(selected_model, selected_size)
            else:
                self.console.print("[yellow]Installation cancelled.[/yellow]")
                
        except KeyboardInterrupt:
            self.console.print("\n[yellow]Operation cancelled by user.[/yellow]")
        except Exception as e:
            self.console.print(f"[red]Error: {e}[/red]")
        finally:
            await self.service.close()
    
    async def _discover_and_display_models(self) -> None:
        """Discover and display available models."""
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            task = progress.add_task("Discovering available models...", total=None)
            
            try:
                self.discovered_models = await self.service.discover_available_models(
                    force_refresh=True
                )
                progress.update(task, description=f"Found {len(self.discovered_models)} models")
                
            except Exception as e:
                progress.update(task, description=f"Discovery failed: {e}")
                raise
        
        if not self.discovered_models:
            self.console.print("[red]No models found. Please check your connection.[/red]")
            return
        
        # Display models in a table
        self._display_models_table()
    
    def _display_models_table(self) -> None:
        """Display discovered models in a formatted table."""
        table = Table(title="Available Ollama Models", show_header=True, header_style="bold magenta")
        
        table.add_column("#", style="dim", width=4)
        table.add_column("Name", style="cyan", min_width=15)
        table.add_column("Description", style="white", min_width=30)
        table.add_column("Sizes", style="green", min_width=10)
        table.add_column("Size (GB)", style="yellow", min_width=8)
        table.add_column("Use Case", style="blue", min_width=15)
        table.add_column("Installed", style="red", min_width=9)
        
        for i, model in enumerate(self.discovered_models, 1):
            # Check if installed
            installed_status = "❌"  # Default to not installed
            
            # Format sizes
            sizes_str = ", ".join(model.sizes) if model.sizes else "N/A"
            
            # Format download size
            size_str = f"{model.download_size_gb:.1f}" if model.download_size_gb else "N/A"
            
            # Truncate description if too long
            description = model.description
            if len(description) > 40:
                description = description[:37] + "..."
            
            table.add_row(
                str(i),
                model.name,
                description,
                sizes_str,
                size_str,
                model.use_case,
                installed_status
            )
        
        self.console.print(table)
    
    async def _get_user_model_selection(self) -> Optional[DiscoveredModel]:
        """Get user's model selection."""
        while True:
            try:
                self.console.print("\n[bold]Select a model to install:[/bold]")
                choice = Prompt.ask(
                    "Enter model number (1-{}) or 'q' to quit".format(len(self.discovered_models)),
                    default="q"
                )
                
                if choice.lower() == 'q':
                    return None
                
                model_index = int(choice) - 1
                if 0 <= model_index < len(self.discovered_models):
                    selected_model = self.discovered_models[model_index]
                    
                    # Display selected model details
                    self._display_model_details(selected_model)
                    
                    if Confirm.ask("Is this the model you want to install?"):
                        return selected_model
                    else:
                        continue
                else:
                    self.console.print("[red]Invalid selection. Please try again.[/red]")
                    
            except ValueError:
                self.console.print("[red]Please enter a valid number.[/red]")
            except KeyboardInterrupt:
                return None
    
    def _display_model_details(self, model: DiscoveredModel) -> None:
        """Display detailed information about a selected model."""
        details = f"""
[bold cyan]{model.name}[/bold cyan]
[dim]Full ID:[/dim] {model.full_identifier}

[bold]Description:[/bold]
{model.description}

[bold]Use Case:[/bold] {model.use_case}
[bold]Capabilities:[/bold] {', '.join(model.capabilities)}
[bold]Available Sizes:[/bold] {', '.join(model.sizes)}
[bold]Download Size:[/bold] {model.download_size_gb:.1f} GB (estimated)
[bold]Categories:[/bold] {', '.join(model.categories)}
"""
        
        self.console.print(Panel(details, title="Model Details", border_style="green"))
    
    async def _get_size_selection(self, model: DiscoveredModel) -> Optional[str]:
        """Get user's size selection if multiple sizes are available."""
        if len(model.sizes) <= 1:
            return model.sizes[0] if model.sizes else "latest"
        
        self.console.print(f"\n[bold]Multiple sizes available for {model.name}:[/bold]")
        
        # Create size options with estimates
        size_options = []
        for size in model.sizes:
            # Estimate memory requirements (rough approximation)
            memory_gb = self._estimate_memory_requirements(size)
            download_gb = self._estimate_size_download(size)
            
            size_options.append(ModelSizeOption(
                size=size,
                download_size_gb=download_gb,
                memory_requirements_gb=memory_gb,
                recommended=(size in ["7B", "8B"])  # Recommend smaller models
            ))
        
        # Display size options table
        table = Table(title=f"Size Options for {model.name}", show_header=True)
        table.add_column("#", style="dim", width=4)
        table.add_column("Size", style="cyan", min_width=8)
        table.add_column("Download (GB)", style="yellow", min_width=12)
        table.add_column("Memory (GB)", style="red", min_width=11)
        table.add_column("Recommended", style="green", min_width=11)
        
        for i, option in enumerate(size_options, 1):
            recommended = "✅" if option.recommended else ""
            table.add_row(
                str(i),
                option.size,
                f"{option.download_size_gb:.1f}",
                f"{option.memory_requirements_gb:.1f}",
                recommended
            )
        
        self.console.print(table)
        
        # Get user selection
        while True:
            try:
                choice = Prompt.ask(
                    f"Select size (1-{len(size_options)}) or 'b' to go back",
                    default="1"
                )
                
                if choice.lower() == 'b':
                    return None
                
                size_index = int(choice) - 1
                if 0 <= size_index < len(size_options):
                    selected_option = size_options[size_index]
                    
                    # Show selection details
                    self.console.print(f"\n[green]Selected: {model.name}:{selected_option.size}[/green]")
                    self.console.print(f"Download size: {selected_option.download_size_gb:.1f} GB")
                    self.console.print(f"Memory requirements: {selected_option.memory_requirements_gb:.1f} GB")
                    
                    return selected_option.size
                else:
                    self.console.print("[red]Invalid selection. Please try again.[/red]")
                    
            except ValueError:
                self.console.print("[red]Please enter a valid number.[/red]")
            except KeyboardInterrupt:
                return None
    
    def _estimate_memory_requirements(self, size: str) -> float:
        """Estimate memory requirements for a model size."""
        size_map = {
            "1B": 2.0,
            "3B": 4.0,
            "7B": 8.0,
            "8B": 8.0,
            "13B": 16.0,
            "30B": 32.0,
            "34B": 32.0,
            "70B": 64.0,
            "180B": 128.0,
        }
        return size_map.get(size, 8.0)  # Default to 8GB
    
    def _estimate_size_download(self, size: str) -> float:
        """Estimate download size for a model size."""
        size_map = {
            "1B": 0.7,
            "3B": 1.8,
            "7B": 4.0,
            "8B": 4.5,
            "13B": 7.5,
            "30B": 17.0,
            "34B": 19.0,
            "70B": 40.0,
            "180B": 100.0,
        }
        return size_map.get(size, 4.0)  # Default to 4GB
    
    async def _confirm_installation(self, model: DiscoveredModel, size: str) -> bool:
        """Confirm installation with user."""
        full_model_id = f"{model.name}:{size}" if size != "latest" else model.full_identifier
        download_size = self._estimate_size_download(size)
        memory_req = self._estimate_memory_requirements(size)
        
        confirmation_text = f"""
[bold]Ready to install:[/bold] {full_model_id}
[bold]Download size:[/bold] ~{download_size:.1f} GB
[bold]Memory required:[/bold] ~{memory_req:.1f} GB
[bold]Use case:[/bold] {model.use_case}

This will download and install the model to your local Ollama instance.
"""
        
        self.console.print(Panel(confirmation_text, title="Installation Confirmation", border_style="yellow"))
        
        return Confirm.ask("Do you want to proceed with the installation?")
    
    async def _install_model(self, model: DiscoveredModel, size: str) -> None:
        """Install the selected model with progress tracking."""
        full_model_id = f"{model.name}:{size}" if size != "latest" else model.full_identifier
        
        self.console.print(f"\n[bold green]Starting installation of {full_model_id}...[/bold green]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console
        ) as progress:
            
            task = progress.add_task("Downloading...", total=100)
            
            try:
                async for download_progress in self.service.install_model(full_model_id):
                    progress.update(
                        task,
                        completed=download_progress.percent,
                        description=f"Downloading {full_model_id}: {download_progress.status}"
                    )
                    
                    if download_progress.is_complete:
                        break
                
                progress.update(task, description="Installation complete!", completed=100)
                self.console.print(f"\n[bold green]✅ Successfully installed {full_model_id}![/bold green]")
                
                # Show usage instructions
                self._show_usage_instructions(full_model_id)
                
            except Exception as e:
                progress.update(task, description=f"Installation failed: {e}")
                self.console.print(f"\n[bold red]❌ Installation failed: {e}[/bold red]")
    
    def _show_usage_instructions(self, model_id: str) -> None:
        """Show usage instructions for the installed model."""
        instructions = f"""
[bold]Model installed successfully![/bold]

You can now use this model in the LONI platform or directly with Ollama:

[bold cyan]Via LONI API:[/bold cyan]
• Use the chat interface in the web application
• Make API calls to /api/chat with model: "{model_id}"

[bold cyan]Via Ollama CLI:[/bold cyan]
• ollama run {model_id}
• ollama chat {model_id}

[bold cyan]Via API:[/bold cyan]
• curl http://localhost:11434/api/generate -d '{{"model":"{model_id}","prompt":"Hello!"}}'
"""
        
        self.console.print(Panel(instructions, title="Usage Instructions", border_style="green"))

    async def _show_installed_models(self) -> None:
        """Show currently installed models."""
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("Loading installed models...", total=None)

                installed_models = await self.service.get_installed_models()
                progress.update(task, description=f"Found {len(installed_models)} installed models")

            if not installed_models:
                self.console.print("[yellow]No models are currently installed.[/yellow]")
                return

            # Display installed models table
            table = Table(title="Installed Ollama Models", show_header=True, header_style="bold green")

            table.add_column("Name", style="cyan", min_width=15)
            table.add_column("Tag", style="blue", min_width=8)
            table.add_column("Type", style="magenta", min_width=10)
            table.add_column("Size (GB)", style="yellow", min_width=10)
            table.add_column("Usage Count", style="green", min_width=12)
            table.add_column("Last Used", style="dim", min_width=15)
            table.add_column("Status", style="red", min_width=10)

            for model in installed_models:
                size_str = f"{model.installed_size_gb:.1f}" if model.installed_size_gb else "N/A"
                last_used = model.last_used_at.strftime("%Y-%m-%d %H:%M") if model.last_used_at else "Never"

                table.add_row(
                    model.name,
                    model.tag,
                    model.model_type,
                    size_str,
                    str(model.usage_count),
                    last_used,
                    model.status
                )

            self.console.print(table)

        except Exception as e:
            self.console.print(f"[red]Error loading installed models: {e}[/red]")


@click.command()
@click.option('--list-installed', is_flag=True, help='List currently installed models')
@click.option('--search', type=str, help='Search for models by name')
@click.option('--category', type=str, help='Filter by category')
def main(list_installed: bool, search: Optional[str], category: Optional[str]):
    """
    LONI Ollama Model Manager - Interactive model selection and installation tool.
    
    This tool helps you discover, select, and install Ollama models with an
    interactive interface showing model details, sizes, and requirements.
    """
    cli = ModelCLI()
    
    try:
        if list_installed:
            # Show installed models
            asyncio.run(cli._show_installed_models())
        else:
            # Run interactive selection
            asyncio.run(cli.run_interactive_selection())
    except KeyboardInterrupt:
        cli.console.print("\n[yellow]Operation cancelled.[/yellow]")
    except Exception as e:
        cli.console.print(f"[red]Error: {e}[/red]")


if __name__ == "__main__":
    main()

apiVersion: 1

datasources:
  # Prometheus datasource
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    basicAuth: false
    jsonData:
      httpMethod: POST
      timeInterval: 15s
      queryTimeout: 60s
      keepCookies: []
    version: 1

  # Jaeger datasource for tracing
  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    editable: true
    basicAuth: false
    jsonData:
      tracesToLogsV2:
        datasourceUid: 'prometheus'
        spanStartTimeShift: '1h'
        spanEndTimeShift: '-1h'
        tags: ['job', 'instance', 'service']
        filterByTraceID: false
        filterBySpanID: false
    version: 1

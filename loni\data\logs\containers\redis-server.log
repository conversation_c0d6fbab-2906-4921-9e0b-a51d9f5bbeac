1:C 13 Jul 2025 04:59:11.542 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
1:C 13 Jul 2025 04:59:11.557 * Redis version=7.4.5, bits=64, commit=00000000, modified=0, pid=1, just started
1:C 13 Jul 2025 04:59:11.567 * Configuration loaded
1:M 13 Jul 2025 04:59:11.576 * monotonic clock: POSIX clock_gettime
1:M 13 Jul 2025 04:59:11.588 * Running mode=standalone, port=6379.
1:M 13 Jul 2025 04:59:11.598 * Server initialized
1:M 13 Jul 2025 04:59:11.621 * Creating AOF base file appendonly.aof.1.base.rdb on server start
1:M 13 Jul 2025 04:59:11.642 * Creating AOF incr file appendonly.aof.1.incr.aof on server start
1:M 13 Jul 2025 04:59:11.652 * Ready to accept connections tcp
1:signal-handler (1752383960) Received SIGTERM scheduling shutdown...
1:M 13 Jul 2025 05:19:20.898 * User requested shutdown...
1:M 13 Jul 2025 05:19:20.907 * Calling fsync() on the AOF file.
1:M 13 Jul 2025 05:19:20.917 * Saving the final RDB snapshot before exiting.
1:M 13 Jul 2025 05:19:20.926 * DB saved on disk
1:M 13 Jul 2025 05:19:20.935 # Redis is now ready to exit, bye bye...
1:C 13 Jul 2025 05:27:39.813 * oO0OoO0OoO0Oo Redis is starting oO0OoO0OoO0Oo
1:C 13 Jul 2025 05:27:39.841 * Redis version=7.4.5, bits=64, commit=00000000, modified=0, pid=1, just started
1:C 13 Jul 2025 05:27:39.852 * Configuration loaded
1:M 13 Jul 2025 05:27:39.866 * monotonic clock: POSIX clock_gettime
1:M 13 Jul 2025 05:27:39.888 * Running mode=standalone, port=6379.
1:M 13 Jul 2025 05:27:39.902 * Server initialized
1:M 13 Jul 2025 05:27:39.914 * Reading RDB base file on AOF loading...
1:M 13 Jul 2025 05:27:39.925 * Loading RDB produced by version 7.4.5
1:M 13 Jul 2025 05:27:39.936 * RDB age 1708 seconds
1:M 13 Jul 2025 05:27:39.952 * RDB memory usage when created 0.90 Mb
1:M 13 Jul 2025 05:27:39.967 * RDB is base AOF
1:M 13 Jul 2025 05:27:39.980 * Done loading RDB, keys loaded: 0, keys expired: 0.
1:M 13 Jul 2025 05:27:39.994 * DB loaded from base file appendonly.aof.1.base.rdb: 0.079 seconds
1:M 13 Jul 2025 05:27:40.007 * DB loaded from append only file: 0.089 seconds
1:M 13 Jul 2025 05:27:40.021 * Opening AOF incr file appendonly.aof.1.incr.aof on server start
1:M 13 Jul 2025 05:27:40.038 * Ready to accept connections tcp
1:M 13 Jul 2025 05:28:42.039 # Possible SECURITY ATTACK detected. It looks like somebody is sending POST or Host: commands to Redis. This is likely due to an attacker attempting to use Cross Protocol Scripting to compromise your Redis instance. Connection from **********:45192 aborted.

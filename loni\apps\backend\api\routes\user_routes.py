"""
User management routes.
"""
import uuid
from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from api.auth import current_active_user, current_superuser, fastapi_users
from api.schemas.user import UserRead, UserUpdate, UserProfile, UserQuotaStatus
from core.database import get_database_session
from core.container import get_container
from core.models.user import User
from core.services.user import UserService


def create_user_router() -> APIRouter:
    """Create user management router."""
    router = APIRouter(prefix="/users", tags=["users"])
    
    # Include FastAPI-Users user routes
    router.include_router(
        fastapi_users.get_users_router(UserRead, UserUpdate),
        prefix="",
        tags=["users"]
    )
    
    @router.get("/me/profile", response_model=UserProfile)
    async def get_user_profile(
        current_user: User = Depends(current_active_user),
        session: AsyncSession = Depends(get_database_session),
        container = Depends(get_container)
    ):
        """Get current user's profile with quota information."""
        async with UserService(session, container) as user_service:
            quota_status = await user_service.get_quota_status(current_user.id)
            
            return UserProfile(
                id=current_user.id,
                email=current_user.email,
                name=current_user.name,
                is_active=current_user.is_active,
                is_verified=current_user.is_verified,
                is_superuser=current_user.is_superuser,
                quota_status=UserQuotaStatus(**quota_status)
            )
    
    @router.get("/me/quota", response_model=UserQuotaStatus)
    async def get_user_quota(
        current_user: User = Depends(current_active_user),
        session: AsyncSession = Depends(get_database_session),
        container = Depends(get_container)
    ):
        """Get current user's quota status."""
        async with UserService(session, container) as user_service:
            quota_status = await user_service.get_quota_status(current_user.id)
            return UserQuotaStatus(**quota_status)
    
    @router.post("/me/quota/reset")
    async def reset_user_quota(
        current_user: User = Depends(current_active_user),
        session: AsyncSession = Depends(get_database_session),
        container = Depends(get_container)
    ):
        """Reset current user's quota (admin only or self-reset if allowed)."""
        async with UserService(session, container) as user_service:
            # For now, only allow superusers to reset quota
            if not current_user.is_superuser:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Only administrators can reset quota"
                )
            
            await user_service.reset_quota(current_user.id)
            return {"message": "Quota reset successfully"}
    
    @router.get("/search", response_model=List[UserRead])
    async def search_users(
        search: str = "",
        limit: int = 50,
        offset: int = 0,
        current_user: User = Depends(current_superuser),
        session: AsyncSession = Depends(get_database_session),
        container = Depends(get_container)
    ):
        """Search users (admin only)."""
        async with UserService(session, container) as user_service:
            users = await user_service.list_users(
                limit=limit,
                offset=offset,
                search=search if search else None
            )
            return [UserRead.model_validate(user) for user in users]
    
    @router.post("/{user_id}/quota/reset")
    async def reset_user_quota_admin(
        user_id: uuid.UUID,
        current_user: User = Depends(current_superuser),
        session: AsyncSession = Depends(get_database_session),
        container = Depends(get_container)
    ):
        """Reset any user's quota (admin only)."""
        async with UserService(session, container) as user_service:
            try:
                await user_service.reset_quota(user_id)
                return {"message": f"Quota reset for user {user_id}"}
            except ValueError as e:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=str(e)
                )
    
    @router.get("/{user_id}/quota", response_model=UserQuotaStatus)
    async def get_user_quota_admin(
        user_id: uuid.UUID,
        current_user: User = Depends(current_superuser),
        session: AsyncSession = Depends(get_database_session),
        container = Depends(get_container)
    ):
        """Get any user's quota status (admin only)."""
        async with UserService(session, container) as user_service:
            try:
                quota_status = await user_service.get_quota_status(user_id)
                return UserQuotaStatus(**quota_status)
            except ValueError as e:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=str(e)
                )
    
    return router
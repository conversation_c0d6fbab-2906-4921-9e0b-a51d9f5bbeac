"""
Database configuration settings.

This module contains all database-related configuration
following the Single Responsibility Principle.
"""

from pydantic import Field
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""

    host: str = "postgres"  # Docker service name
    port: int = Field(default=5432, alias="POSTGRES_PORT")
    database: str = Field(default="loni", alias="POSTGRES_DB")
    username: str = Field(default="loni", alias="POSTGRES_USER")
    password: str = Field(..., description="Database password", alias="POSTGRES_PASSWORD")

    # Connection pool settings
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600

    # SSL settings
    ssl_mode: str = "prefer"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    @property
    def url(self) -> str:
        """Construct database URL."""
        return f"postgresql+asyncpg://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"


class QdrantSettings(BaseSettings):
    """Qdrant vector database settings."""
    
    host: str = "localhost"
    port: int = 6333
    collection_name: str = "documents"
    vector_size: int = 1536  # OpenAI embedding dimension
    distance_metric: str = "cosine"
    
    # Connection settings
    timeout: int = 60
    prefer_grpc: bool = True
    
    class Config:
        env_prefix = "QDRANT_"
    
    @property
    def url(self) -> str:
        """Construct Qdrant URL."""
        return f"http://{self.host}:{self.port}"
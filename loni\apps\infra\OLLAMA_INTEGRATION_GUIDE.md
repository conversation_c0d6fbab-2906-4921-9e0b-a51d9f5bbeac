# LONI Ollama Integration - Complete Setup Guide

## 🎯 **Integration Overview**

The LONI Ollama Model Management System is now fully integrated with the Docker infrastructure, providing seamless model discovery, installation, and management capabilities within the containerized environment.

### ✅ **Integration Features**

- **🐳 Docker Integration**: Ollama runs as a containerized service with proper networking
- **📦 Volume Management**: Persistent model storage with shared access between containers
- **🔗 Service Dependencies**: Backend automatically waits for Ollama to be healthy
- **🌐 Network Connectivity**: Internal Docker network communication between services
- **🖥️ CLI Access**: Model management CLI works within containerized environment
- **🔌 API Integration**: REST API endpoints fully functional with containerized Ollama

## 🚀 **Quick Start**

### **1. Start the Ollama Service**

```bash
# Navigate to infrastructure directory
cd loni/apps/infra

# Start Ollama CPU service (default)
docker compose up ollama-cpu -d

# OR start GPU service (if NVIDIA GPU available)
docker compose --profile gpu up ollama -d

# Verify Ollama is running
docker compose ps | grep ollama
curl http://localhost:11434/api/tags
```

### **2. Restart Backend with Ollama Dependency**

```bash
# Restart backend to establish Ollama connection
docker compose restart backend

# Verify backend can connect to Ollama
docker exec loni-backend curl -f http://ollama-cpu:11434/api/tags
```

### **3. Test Model Discovery**

```bash
# Test model discovery API
curl "http://localhost:8000/api/models/available?page=1&page_size=5"

# Test CLI tool
docker exec loni-backend python manage.py models --help
```

### **4. Install Your First Model**

```bash
# Interactive model selection (recommended)
docker exec -it loni-backend python manage.py models select

# OR install directly
docker exec loni-backend python manage.py models install llama3.2:latest

# OR via API
curl -X POST "http://localhost:8000/api/models/install" \
  -H "Content-Type: application/json" \
  -d '{"model_id": "llama3.2:latest"}'
```

## 🔧 **Configuration Details**

### **Docker Compose Configuration**

The integration includes the following key configurations:

#### **Backend Service Updates:**
```yaml
backend:
  volumes:
    - ../../data/models/ollama:/app/data/models/ollama  # Model storage
  depends_on:
    ollama-cpu:
      condition: service_healthy  # Wait for Ollama
  environment:
    OLLAMA_BASE_URL: http://ollama-cpu:11434
    OLLAMA_HOST: ollama-cpu
    MODELS_DIR: /app/data/models/ollama
```

#### **Ollama Service Configuration:**
```yaml
ollama-cpu:
  image: ollama/ollama:latest
  container_name: loni-ollama-cpu
  environment:
    OLLAMA_HOST: 0.0.0.0:11434
    OLLAMA_ORIGINS: "*"
    OLLAMA_MODELS: /root/.ollama/models
  volumes:
    - ollama_data:/root/.ollama
    - ../../data/models/ollama:/shared/models
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
    start_period: 60s
```

### **Environment Variables**

Updated `.env` file includes:
```bash
OLLAMA_PORT=11434
OLLAMA_HOST=ollama-cpu
OLLAMA_BASE_URL=http://ollama-cpu:11434
```

### **Volume Mounts**

- **`ollama_data`**: Persistent Ollama data and models
- **`../../data/models/ollama`**: Shared model directory accessible from backend
- **Configuration**: Ollama config files mounted from `./config/ollama`

## 🧪 **Testing and Validation**

### **1. Run Integration Test Suite**

```bash
# Execute comprehensive tests
cd loni/apps/infra
./test_ollama_integration.sh

# This tests:
# - Container status and health
# - Network connectivity between services
# - API endpoint functionality
# - Volume mount accessibility
# - CLI tool operation
```

### **2. Manual Verification Steps**

```bash
# Check all containers are running
docker compose ps

# Test Ollama health
curl http://localhost:11434/api/tags

# Test backend-Ollama connectivity
docker exec loni-backend curl -f http://ollama-cpu:11434/api/tags

# Test model discovery
curl "http://localhost:8000/api/models/available" | jq '.total'

# Test CLI access
docker exec loni-backend python manage.py models list --help
```

### **3. Model Management Workflow Test**

```bash
# 1. Discover models
docker exec loni-backend python manage.py models list

# 2. Install a model
docker exec loni-backend python manage.py models install llama3.2:latest

# 3. Check installation status
docker exec loni-backend python manage.py models status llama3.2:latest

# 4. List installed models
docker exec loni-backend python manage.py models list --installed-only

# 5. Test model via Ollama
docker exec loni-ollama-cpu ollama list
```

## 🔍 **Troubleshooting**

### **Common Issues and Solutions**

#### **1. Ollama Container Not Starting**
```bash
# Check logs
docker logs loni-ollama-cpu --tail 50

# Verify volume mounts
docker volume inspect loni_ollama_data

# Check port conflicts
netstat -an | grep 11434
```

#### **2. Backend Cannot Connect to Ollama**
```bash
# Test network connectivity
docker exec loni-backend ping ollama-cpu
docker exec loni-backend nslookup ollama-cpu

# Check environment variables
docker exec loni-backend env | grep OLLAMA

# Verify service dependencies
docker compose ps ollama-cpu
```

#### **3. Model Downloads Fail**
```bash
# Check disk space
df -h
docker exec loni-ollama-cpu df -h /root/.ollama

# Test manual download
docker exec loni-ollama-cpu ollama pull llama3.2:latest

# Check permissions
docker exec loni-ollama-cpu ls -la /root/.ollama/models
```

#### **4. CLI Tool Issues**
```bash
# Test CLI access
docker exec loni-backend python manage.py models --help

# Check Python dependencies
docker exec loni-backend pip list | grep -E "(httpx|rich|click)"

# Verify model directory
docker exec loni-backend ls -la /app/data/models/ollama/
```

## 📊 **Monitoring and Maintenance**

### **Health Monitoring**

```bash
# Check service health
docker compose ps | grep -E "(backend|ollama)"

# Monitor resource usage
docker stats loni-backend loni-ollama-cpu

# Check logs
docker compose logs backend | grep -i ollama
docker compose logs ollama-cpu | tail -20
```

### **Model Storage Management**

```bash
# Check model storage usage
docker exec loni-ollama-cpu du -sh /root/.ollama/models
ls -lah loni/data/models/ollama/

# Clean up unused models
docker exec loni-ollama-cpu ollama rm <model-name>
docker exec loni-backend python manage.py models uninstall <model-id>
```

### **Performance Optimization**

```bash
# For CPU-only deployments, adjust memory limits
docker compose up ollama-cpu -d --memory=8g

# Monitor model loading times
docker exec loni-ollama-cpu time ollama run llama3.2:latest "Hello"

# Check concurrent request handling
curl -X POST http://localhost:11434/api/generate \
  -d '{"model":"llama3.2:latest","prompt":"Test"}'
```

## 🎯 **Next Steps**

### **1. Production Deployment**
- Configure resource limits for Ollama container
- Set up model backup and restore procedures
- Implement monitoring and alerting for model services

### **2. Advanced Features**
- Configure GPU support for faster inference
- Set up model versioning and rollback capabilities
- Implement model usage analytics and optimization

### **3. Integration with Frontend**
- Add model selection UI to web interface
- Implement real-time model download progress
- Create model management dashboard

## 📚 **Additional Resources**

- **Comprehensive Documentation**: `loni/apps/backend/integrations/ollama/README.md`
- **Docker Infrastructure Guide**: `loni/apps/infra/docker.md`
- **API Documentation**: Available at `http://localhost:8000/docs` when backend is running
- **Backend Setup Guide**: `loni/apps/backend/OLLAMA_SETUP.md`

## ✅ **Integration Status**

**🎉 OLLAMA INTEGRATION COMPLETE!**

The LONI Ollama Model Management System is now fully integrated with the Docker infrastructure and ready for production use. All components are working together seamlessly:

- ✅ **Docker Services**: Ollama and Backend containers properly configured
- ✅ **Network Connectivity**: Internal service communication established
- ✅ **Volume Management**: Persistent model storage configured
- ✅ **API Integration**: REST endpoints fully functional
- ✅ **CLI Tools**: Interactive model management available
- ✅ **Health Monitoring**: Comprehensive health checks implemented
- ✅ **Documentation**: Complete setup and troubleshooting guides provided

The system is production-ready and provides a robust foundation for AI model management within the LONI platform! 🚀

# Ollama Configuration for LONI Platform
# This configuration file provides settings for the Ollama service
# running within the LONI Docker infrastructure.

# Server Configuration
OLLAMA_HOST=0.0.0.0:11434
OLLAMA_ORIGINS=*

# Model Storage Configuration
OLLAMA_MODELS=/root/.ollama/models

# Performance Configuration
OLLAMA_NUM_PARALLEL=1
OLLAMA_MAX_LOADED_MODELS=1
OLLAMA_MAX_QUEUE=512

# Logging Configuration
OLLAMA_DEBUG=false
OLLAMA_VERBOSE=false

# Memory Configuration (adjust based on available system memory)
# For CPU-only deployments, these settings help manage memory usage
OLLAMA_MAX_VRAM=0  # Use system RAM instead of VRAM for CPU-only

# Network Configuration
OLLAMA_KEEP_ALIVE=5m
OLLAMA_TIMEOUT=30s

# Security Configuration
OLLAMA_NOPRUNE=false  # Allow model pruning to save space

# Integration with LONI Backend
# The backend will connect via: http://ollama-cpu:11434 or http://ollama:11434
# Models will be managed through the LONI model management API

# Model Download Configuration
# Models will be downloaded to the shared volume for persistence
# and accessibility from the LONI backend service

#!/usr/bin/env python3
"""
LONI Backend Management Script.

This script provides management commands for the LONI backend including
database migrations, model management, and other administrative tasks.
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import click
from loguru import logger

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from utils.model_cli import ModelCLI


@click.group()
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
def cli(verbose: bool):
    """LONI Backend Management Commands."""
    if verbose:
        logger.add(sys.stderr, level="DEBUG")
    else:
        logger.add(sys.stderr, level="INFO")


@cli.group()
def models():
    """Ollama model management commands."""
    pass


@models.command('select')
@click.option('--search', type=str, help='Search for models by name')
@click.option('--category', type=str, help='Filter by category')
@click.option('--list-installed', is_flag=True, help='List currently installed models')
def select_model(search: Optional[str], category: Optional[str], list_installed: bool):
    """
    Interactive model selection and installation tool.
    
    This command provides an interactive interface for discovering,
    selecting, and installing Ollama models.
    """
    model_cli = ModelCLI()
    
    try:
        if list_installed:
            asyncio.run(model_cli._show_installed_models())
        else:
            asyncio.run(model_cli.run_interactive_selection())
    except KeyboardInterrupt:
        click.echo("\nOperation cancelled by user.")
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@models.command('list')
@click.option('--installed-only', is_flag=True, help='Show only installed models')
@click.option('--available-only', is_flag=True, help='Show only available models')
@click.option('--format', type=click.Choice(['table', 'json', 'csv']), default='table', help='Output format')
def list_models(installed_only: bool, available_only: bool, format: str):
    """List available and installed models."""
    from integrations.ollama.management import ModelManagementService
    import json
    
    async def _list_models():
        service = ModelManagementService()
        try:
            if installed_only:
                models = await service.get_installed_models()
                model_data = [model.to_dict() for model in models]
            elif available_only:
                models = await service.discover_available_models()
                model_data = [model.dict() for model in models]
            else:
                # Show both
                installed = await service.get_installed_models()
                available = await service.discover_available_models()
                
                model_data = {
                    "installed": [model.to_dict() for model in installed],
                    "available": [model.dict() for model in available]
                }
            
            if format == 'json':
                click.echo(json.dumps(model_data, indent=2, default=str))
            elif format == 'csv':
                # Simple CSV output for single model lists
                if isinstance(model_data, list) and model_data:
                    import csv
                    import io
                    
                    output = io.StringIO()
                    writer = csv.DictWriter(output, fieldnames=model_data[0].keys())
                    writer.writeheader()
                    writer.writerows(model_data)
                    click.echo(output.getvalue())
                else:
                    click.echo("CSV format not supported for combined output")
            else:
                # Table format (default)
                if isinstance(model_data, list):
                    if not model_data:
                        click.echo("No models found.")
                    else:
                        for model in model_data:
                            click.echo(f"• {model.get('name', model.get('full_name', 'Unknown'))}")
                else:
                    click.echo(f"Installed models: {len(model_data['installed'])}")
                    click.echo(f"Available models: {len(model_data['available'])}")
        
        finally:
            await service.close()
    
    try:
        asyncio.run(_list_models())
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@models.command('install')
@click.argument('model_id')
@click.option('--force', is_flag=True, help='Force reinstall if already installed')
def install_model(model_id: str, force: bool):
    """Install a specific model."""
    from integrations.ollama.management import ModelManagementService
    
    async def _install_model():
        service = ModelManagementService()
        try:
            click.echo(f"Installing model: {model_id}")
            
            # Check if already installed
            if not force:
                installed = await service.get_installed_models()
                if any(model.full_name == model_id for model in installed):
                    click.echo(f"Model {model_id} is already installed. Use --force to reinstall.")
                    return
            
            # Install with progress
            async for progress in service.install_model(model_id):
                click.echo(f"Progress: {progress.percent:.1f}% - {progress.status}")
                if progress.is_complete:
                    break
            
            click.echo(f"Successfully installed {model_id}")
            
        except Exception as e:
            click.echo(f"Installation failed: {e}", err=True)
            sys.exit(1)
        finally:
            await service.close()
    
    try:
        asyncio.run(_install_model())
    except KeyboardInterrupt:
        click.echo("\nInstallation cancelled by user.")
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@models.command('uninstall')
@click.argument('model_id')
@click.option('--yes', is_flag=True, help='Skip confirmation prompt')
def uninstall_model(model_id: str, yes: bool):
    """Uninstall a specific model."""
    from integrations.ollama.management import ModelManagementService
    
    if not yes:
        if not click.confirm(f"Are you sure you want to uninstall {model_id}?"):
            click.echo("Uninstallation cancelled.")
            return
    
    async def _uninstall_model():
        service = ModelManagementService()
        try:
            click.echo(f"Uninstalling model: {model_id}")
            
            success = await service.uninstall_model(model_id)
            
            if success:
                click.echo(f"Successfully uninstalled {model_id}")
            else:
                click.echo(f"Failed to uninstall {model_id}")
                sys.exit(1)
                
        except Exception as e:
            click.echo(f"Uninstallation failed: {e}", err=True)
            sys.exit(1)
        finally:
            await service.close()
    
    try:
        asyncio.run(_uninstall_model())
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@models.command('status')
@click.argument('model_id', required=False)
def model_status(model_id: Optional[str]):
    """Get status of a specific model or all models."""
    from integrations.ollama.management import ModelManagementService
    import json
    
    async def _get_status():
        service = ModelManagementService()
        try:
            if model_id:
                status = await service.get_model_status(model_id)
                click.echo(json.dumps(status, indent=2, default=str))
            else:
                # Show status of all installed models
                installed = await service.get_installed_models()
                for model in installed:
                    status = await service.get_model_status(model.full_name)
                    click.echo(f"{model.full_name}: {status['status']}")
                    
        except Exception as e:
            click.echo(f"Error: {e}", err=True)
            sys.exit(1)
        finally:
            await service.close()
    
    try:
        asyncio.run(_get_status())
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.group()
def db():
    """Database management commands."""
    pass


@db.command('migrate')
def migrate():
    """Run database migrations."""
    import subprocess
    
    try:
        # Run Alembic migrations
        result = subprocess.run(['alembic', 'upgrade', 'head'], 
                              capture_output=True, text=True, cwd=Path(__file__).parent)
        
        if result.returncode == 0:
            click.echo("Database migrations completed successfully.")
            if result.stdout:
                click.echo(result.stdout)
        else:
            click.echo("Migration failed:", err=True)
            if result.stderr:
                click.echo(result.stderr, err=True)
            sys.exit(1)
            
    except FileNotFoundError:
        click.echo("Alembic not found. Please install alembic: pip install alembic", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"Migration error: {e}", err=True)
        sys.exit(1)


@db.command('revision')
@click.option('--message', '-m', required=True, help='Migration message')
@click.option('--autogenerate', is_flag=True, help='Auto-generate migration from model changes')
def create_revision(message: str, autogenerate: bool):
    """Create a new database migration."""
    import subprocess
    
    try:
        cmd = ['alembic', 'revision', '-m', message]
        if autogenerate:
            cmd.append('--autogenerate')
        
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=Path(__file__).parent)
        
        if result.returncode == 0:
            click.echo("Migration created successfully.")
            if result.stdout:
                click.echo(result.stdout)
        else:
            click.echo("Failed to create migration:", err=True)
            if result.stderr:
                click.echo(result.stderr, err=True)
            sys.exit(1)
            
    except FileNotFoundError:
        click.echo("Alembic not found. Please install alembic: pip install alembic", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"Migration creation error: {e}", err=True)
        sys.exit(1)


if __name__ == '__main__':
    cli()

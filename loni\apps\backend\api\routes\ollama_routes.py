"""
Ollama model management API routes.

This module provides FastAPI routes for Ollama model discovery, installation,
and management functionality.
"""

import asyncio
from typing import List, Optional

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Query
from fastapi.responses import StreamingResponse
from loguru import logger

from api.schemas.ollama_schemas import (
    AvailableModelsResponse, AvailableModelResponse, InstalledModelResponse,
    ModelInstallRequest, ModelInstallResponse, ModelStatusResponse,
    ModelUninstallResponse, ModelSearchRequest, ErrorResponse,
    ModelDownloadProgressResponse
)
from core.container import get_container
from integrations.ollama.management import ModelManagementService, ModelInstallationError


def create_ollama_router() -> APIRouter:
    """Create and configure the Ollama routes."""
    
    router = APIRouter(prefix="/api/models", tags=["Ollama Models"])
    
    async def get_model_service() -> ModelManagementService:
        """Dependency to get model management service."""
        container = get_container()
        # In a real implementation, this would be injected from the container
        return ModelManagementService()
    
    @router.get(
        "/available",
        response_model=AvailableModelsResponse,
        summary="Get available models",
        description="Discover and return available models from Ollama registry with pagination and filtering"
    )
    async def get_available_models(
        query: Optional[str] = Query(None, description="Search query"),
        category: Optional[str] = Query(None, description="Category filter"),
        page: int = Query(1, ge=1, description="Page number"),
        page_size: int = Query(20, ge=1, le=100, description="Page size"),
        force_refresh: bool = Query(False, description="Force refresh cache"),
        service: ModelManagementService = Depends(get_model_service)
    ) -> AvailableModelsResponse:
        """
        Get available models from Ollama registry.
        
        This endpoint scrapes the Ollama registry and returns structured data
        for each model including capabilities, sizes, and metadata.
        """
        try:
            # Discover models
            discovered_models = await service.discover_available_models(
                force_refresh=force_refresh,
                search_query=query,
                category=category
            )
            
            # Get installed models to mark them
            installed_models = await service.get_installed_models()
            installed_names = {model.full_name for model in installed_models}
            
            # Convert to response format
            available_models = []
            for model in discovered_models:
                available_models.append(AvailableModelResponse(
                    name=model.name,
                    full_identifier=model.full_identifier,
                    description=model.description,
                    use_case=model.use_case,
                    sizes=model.sizes,
                    capabilities=model.capabilities,
                    last_updated=model.last_updated,
                    download_size_gb=model.download_size_gb,
                    tags=model.tags,
                    categories=model.categories,
                    registry_url=model.registry_url,
                    is_installed=model.full_identifier in installed_names
                ))
            
            # Apply pagination
            total = len(available_models)
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            paginated_models = available_models[start_idx:end_idx]
            
            total_pages = (total + page_size - 1) // page_size
            
            return AvailableModelsResponse(
                models=paginated_models,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages,
                has_next=page < total_pages,
                has_previous=page > 1
            )
            
        except Exception as e:
            logger.error(f"Failed to get available models: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to discover models: {str(e)}"
            )
    
    @router.get(
        "/installed",
        response_model=List[InstalledModelResponse],
        summary="Get installed models",
        description="Get all locally installed Ollama models with metadata"
    )
    async def get_installed_models(
        service: ModelManagementService = Depends(get_model_service)
    ) -> List[InstalledModelResponse]:
        """
        Get all locally installed models.
        
        Returns detailed information about each installed model including
        usage statistics and installation metadata.
        """
        try:
            installed_models = await service.get_installed_models()
            
            return [
                InstalledModelResponse(
                    id=str(model.id),
                    name=model.name,
                    tag=model.tag,
                    full_name=model.full_name,
                    description=model.description,
                    model_type=model.model_type,
                    capabilities=model.capabilities,
                    parameter_count=model.parameter_count,
                    download_size_gb=model.download_size_gb,
                    status=model.status,
                    is_installed=model.is_installed,
                    installation_path=model.installation_path,
                    installed_size_gb=model.installed_size_gb,
                    usage_count=model.usage_count,
                    last_used_at=model.last_used_at,
                    created_at=model.created_at,
                    updated_at=model.updated_at
                )
                for model in installed_models
            ]
            
        except Exception as e:
            logger.error(f"Failed to get installed models: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get installed models: {str(e)}"
            )
    
    @router.post(
        "/install",
        response_model=ModelInstallResponse,
        summary="Install a model",
        description="Start installation of a model with progress tracking"
    )
    async def install_model(
        request: ModelInstallRequest,
        background_tasks: BackgroundTasks,
        service: ModelManagementService = Depends(get_model_service)
    ) -> ModelInstallResponse:
        """
        Install a model.
        
        Starts the model installation process and returns immediately.
        Use the status endpoint to track progress.
        """
        try:
            model_id = request.model_id
            
            # Check if already installed
            if not request.force_reinstall:
                installed_models = await service.get_installed_models()
                if any(model.full_name == model_id for model in installed_models):
                    raise HTTPException(
                        status_code=400,
                        detail=f"Model {model_id} is already installed. Use force_reinstall=true to reinstall."
                    )
            
            # Start installation in background
            async def install_task():
                try:
                    async for progress in service.install_model(model_id):
                        # Progress is tracked internally
                        pass
                except Exception as e:
                    logger.error(f"Background installation failed for {model_id}: {e}")
            
            background_tasks.add_task(install_task)
            
            return ModelInstallResponse(
                model_id=model_id,
                status="started",
                message="Model installation started",
                download_url=f"/api/models/status/{model_id}"
            )
            
        except ModelInstallationError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            logger.error(f"Failed to start model installation: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to start installation: {str(e)}"
            )
    
    @router.delete(
        "/uninstall/{model_id:path}",
        response_model=ModelUninstallResponse,
        summary="Uninstall a model",
        description="Remove a model from local storage"
    )
    async def uninstall_model(
        model_id: str,
        service: ModelManagementService = Depends(get_model_service)
    ) -> ModelUninstallResponse:
        """
        Uninstall a model.
        
        Removes the model from local storage and updates the registry.
        """
        try:
            success = await service.uninstall_model(model_id)
            
            if success:
                return ModelUninstallResponse(
                    model_id=model_id,
                    success=True,
                    message="Model uninstalled successfully"
                )
            else:
                return ModelUninstallResponse(
                    model_id=model_id,
                    success=False,
                    message="Failed to uninstall model"
                )
                
        except Exception as e:
            logger.error(f"Failed to uninstall model {model_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to uninstall model: {str(e)}"
            )
    
    @router.get(
        "/status/{model_id:path}",
        response_model=ModelStatusResponse,
        summary="Get model status",
        description="Get detailed status of a specific model including download progress"
    )
    async def get_model_status(
        model_id: str,
        service: ModelManagementService = Depends(get_model_service)
    ) -> ModelStatusResponse:
        """
        Get model status.
        
        Returns detailed status information including download progress
        if the model is currently being downloaded.
        """
        try:
            status_info = await service.get_model_status(model_id)
            
            return ModelStatusResponse(
                model_id=status_info["model_id"],
                status=status_info["status"],
                is_installed=status_info.get("is_installed", False),
                download_progress=status_info.get("download_progress"),
                installation_path=status_info.get("installation_path"),
                installed_size_gb=status_info.get("installed_size_gb"),
                last_used_at=status_info.get("last_used_at"),
                usage_count=status_info.get("usage_count", 0),
                metadata=status_info.get("metadata", {}),
                message=status_info.get("message")
            )
            
        except Exception as e:
            logger.error(f"Failed to get model status for {model_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get model status: {str(e)}"
            )
    
    @router.get(
        "/progress/{model_id:path}",
        response_model=ModelDownloadProgressResponse,
        summary="Get download progress",
        description="Get real-time download progress for a model"
    )
    async def get_download_progress(
        model_id: str,
        service: ModelManagementService = Depends(get_model_service)
    ) -> ModelDownloadProgressResponse:
        """
        Get download progress.
        
        Returns current download progress for a model that is being downloaded.
        """
        try:
            progress = await service.get_download_progress(model_id)
            
            if not progress:
                raise HTTPException(
                    status_code=404,
                    detail=f"No download in progress for model {model_id}"
                )
            
            return ModelDownloadProgressResponse(
                model_name=progress.model_name,
                status=progress.status,
                completed=progress.completed,
                total=progress.total,
                percent=progress.percent,
                digest=progress.digest,
                is_complete=progress.is_complete
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to get download progress for {model_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get download progress: {str(e)}"
            )
    
    @router.post(
        "/cancel/{model_id:path}",
        summary="Cancel download",
        description="Cancel an ongoing model download"
    )
    async def cancel_download(
        model_id: str,
        service: ModelManagementService = Depends(get_model_service)
    ) -> dict:
        """
        Cancel download.
        
        Cancels an ongoing model download and cleans up resources.
        """
        try:
            success = await service.cancel_download(model_id)
            
            if success:
                return {
                    "model_id": model_id,
                    "success": True,
                    "message": "Download cancelled successfully"
                }
            else:
                return {
                    "model_id": model_id,
                    "success": False,
                    "message": "No download to cancel or cancellation failed"
                }
                
        except Exception as e:
            logger.error(f"Failed to cancel download for {model_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to cancel download: {str(e)}"
            )
    
    return router

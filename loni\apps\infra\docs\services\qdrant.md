# Qdrant Vector Database Service Documentation

## Service Overview

Qdrant serves as the vector database for the LONI platform, providing high-performance storage and similarity search for AI embeddings, semantic search, and machine learning applications. The service uses the official `qdrant/qdrant:latest` image, which provides a production-ready vector database with REST and gRPC APIs.

### Role in LONI Platform
- **Vector Storage**: Stores embeddings from AI models (OpenAI, Anthropic, local models)
- **Semantic Search**: Enables similarity search across documents, conversations, and content
- **RAG Support**: Provides retrieval capabilities for Retrieval-Augmented Generation
- **AI Memory**: Stores conversation context and user interaction embeddings
- **Content Discovery**: Powers recommendation systems and content matching
- **Knowledge Base**: Stores and retrieves structured knowledge representations

## Docker Configuration

```yaml
qdrant:
  image: qdrant/qdrant:latest
  container_name: loni-qdrant
  environment:
    QDRANT__SERVICE__HTTP_PORT: 6333
    QDRANT__SERVICE__GRPC_PORT: 6334
  volumes:
    - qdrant_data:/qdrant/storage
    - ./config/qdrant/config.yaml:/qdrant/config/production.yaml:ro
    - ../../data/logs/containers:/var/log/qdrant
  ports:
    - "${QDRANT_PORT:-6333}:6333"
    - "${QDRANT_GRPC_PORT:-6334}:6334"
  healthcheck:
    test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:6333/health || exit 1"]
    interval: 30s
    timeout: 10s
    retries: 5
    start_period: 30s
  restart: unless-stopped
  networks:
    - loni-network
```

## Environment Variables

### Service Configuration
| Variable | Description | Default | Notes |
|----------|-------------|---------|-------|
| `QDRANT__SERVICE__HTTP_PORT` | HTTP API port | `6333` | REST API endpoint |
| `QDRANT__SERVICE__GRPC_PORT` | gRPC API port | `6334` | High-performance API |
| `QDRANT_PORT` | External HTTP port mapping | `6333` | Docker port mapping |
| `QDRANT_GRPC_PORT` | External gRPC port mapping | `6334` | Docker port mapping |

### Advanced Configuration Variables
| Variable | Description | Default | Use Case |
|----------|-------------|---------|----------|
| `QDRANT__LOG_LEVEL` | Logging level | `INFO` | DEBUG, INFO, WARN, ERROR |
| `QDRANT__SERVICE__MAX_REQUEST_SIZE_MB` | Max request size | `32` | Large batch operations |
| `QDRANT__SERVICE__API_KEY` | API authentication key | None | Security in production |
| `QDRANT__SERVICE__ENABLE_CORS` | Enable CORS headers | `true` | Web application access |
| `QDRANT__STORAGE__STORAGE_PATH` | Data storage path | `/qdrant/storage` | Custom storage location |
| `QDRANT__TELEMETRY_DISABLED` | Disable telemetry | `false` | Privacy compliance |

### Performance Tuning Variables
| Variable | Description | Default | Performance Impact |
|----------|-------------|---------|-------------------|
| `QDRANT__STORAGE__PERFORMANCE__MAX_SEARCH_THREADS` | Search thread count | `0` (auto) | Search performance |
| `QDRANT__STORAGE__HNSW_INDEX__M` | HNSW graph edges | `16` | Search accuracy vs memory |
| `QDRANT__STORAGE__HNSW_INDEX__EF_CONSTRUCT` | Index build parameter | `100` | Build time vs accuracy |
| `QDRANT__STORAGE__ON_DISK_PAYLOAD` | Store payloads on disk | `true` | Memory vs I/O trade-off |

## Volume Mounts

### Data Persistence
- **Primary Storage**: `qdrant_data:/qdrant/storage`
  - Contains vector collections, indexes, and metadata
  - **Backup Strategy**: Snapshot-based backups via Qdrant API
  - **Size Estimation**: Varies by vector dimensions and collection size

### Configuration
- **Production Config**: `./config/qdrant/config.yaml:/qdrant/config/production.yaml:ro`
  - Custom Qdrant configuration file
  - **Environment**: Uses `RUN_MODE=production` by default
  - **Read-only**: Prevents runtime modification

### Log Files
- **Container Logs**: `../../data/logs/containers:/var/log/qdrant`
  - Qdrant server logs and operation traces
  - **Retention**: 30 days, rotated daily
  - **Format**: Structured JSON logging

## Network Configuration

### Port Mappings
- **HTTP API**: `6333:6333` (REST API)
- **gRPC API**: `6334:6334` (High-performance API)
- **External Access**: Configurable via environment variables
- **Internal Access**: Available on `loni-network` as `qdrant:6333` and `qdrant:6334`

### API Endpoints
- **Health Check**: `GET /health`
- **Collections**: `GET /collections`
- **Search**: `POST /collections/{collection}/points/search`
- **Insert**: `PUT /collections/{collection}/points`
- **Metrics**: `GET /metrics` (Prometheus format)

### Inter-Service Communication
- **Backend API**: Primary connection for vector operations
- **AI Services**: Embedding storage and retrieval
- **Search Services**: Semantic search and recommendations
- **Monitoring**: Prometheus metrics collection

## Health Checks

### Primary Health Check
```bash
wget --no-verbose --tries=1 --spider http://localhost:6333/health
```
- **Interval**: 30 seconds
- **Timeout**: 10 seconds
- **Retries**: 5 attempts
- **Start Period**: 30 seconds (allows initialization)

### Advanced Health Monitoring
```bash
# Check service status
curl http://localhost:6333/health

# List collections
curl http://localhost:6333/collections

# Check cluster status (if clustered)
curl http://localhost:6333/cluster

# Memory and performance metrics
curl http://localhost:6333/metrics
```

## Security Configuration

### Authentication
- **API Key**: Configurable via `QDRANT__SERVICE__API_KEY`
- **Read-only API Key**: Separate key for read operations
- **JWT RBAC**: Role-based access control support

### Network Security
- **Internal Access**: No direct external exposure in production
- **TLS Support**: Configurable via `QDRANT__SERVICE__ENABLE_TLS`
- **CORS**: Configurable for web application access

### Recommended Security Hardening
```yaml
# config.yaml security settings
service:
  api_key: ${QDRANT_API_KEY}
  read_only_api_key: ${QDRANT_READ_ONLY_API_KEY}
  enable_tls: true
  verify_https_client_certificate: true
  enable_cors: false  # Disable in production

tls:
  cert: ./tls/cert.pem
  key: ./tls/key.pem
  ca_cert: ./tls/cacert.pem
```

## Performance Tuning

### Memory Configuration
```yaml
storage:
  # Store payloads on disk to save RAM
  on_disk_payload: true
  
  # HNSW index optimization
  hnsw_index:
    m: 16                    # Balance between accuracy and memory
    ef_construct: 100        # Higher = better accuracy, slower build
    max_indexing_threads: 8  # Parallel index building
    on_disk: false          # Keep index in memory for speed

  performance:
    max_search_threads: 0    # Auto-detect based on CPU cores
```

### Collection Optimization
```yaml
# Per-collection settings
vectors:
  on_disk: false            # Keep vectors in memory for speed

quantization:
  scalar:
    type: int8              # Reduce memory usage
    quantile: 0.99
```

### Storage Configuration
```yaml
storage:
  optimizers:
    deleted_threshold: 0.2   # Optimize when 20% deleted
    vacuum_min_vector_number: 1000
    default_segment_number: 0  # Auto-select based on CPU
    max_segment_size_kb: 200000  # 200MB segments
```

## Troubleshooting

### Common Issues

#### Container Won't Start
```bash
# Check logs
docker logs loni-qdrant

# Common causes:
# 1. Invalid configuration file
# 2. Port conflicts
# 3. Insufficient memory
# 4. Storage permission issues
```

#### Health Check Failures
```bash
# Test HTTP API
curl http://localhost:6333/health

# Test gRPC API (requires grpcurl)
grpcurl -plaintext localhost:6334 qdrant.Qdrant/HealthCheck

# Check service binding
docker exec loni-qdrant netstat -tlnp | grep -E "(6333|6334)"
```

#### Performance Issues
```bash
# Check memory usage
curl http://localhost:6333/metrics | grep memory

# Check collection statistics
curl http://localhost:6333/collections/{collection}

# Monitor search performance
curl -X POST http://localhost:6333/collections/{collection}/points/search \
  -H "Content-Type: application/json" \
  -d '{"vector": [...], "limit": 10, "with_payload": true}'
```

#### Storage Issues
```bash
# Check disk usage
docker exec loni-qdrant df -h /qdrant/storage

# Check collection sizes
curl http://localhost:6333/collections | jq '.result.collections[].points_count'

# Optimize collections
curl -X POST http://localhost:6333/collections/{collection}/index
```

## Backup and Recovery

### Snapshot-Based Backup
```bash
# Create snapshot
curl -X POST http://localhost:6333/collections/{collection}/snapshots

# List snapshots
curl http://localhost:6333/collections/{collection}/snapshots

# Download snapshot
curl http://localhost:6333/collections/{collection}/snapshots/{snapshot_name} \
  --output backup.snapshot
```

### Full Database Backup
```bash
# Create full snapshot
curl -X POST http://localhost:6333/snapshots

# Download full snapshot
curl http://localhost:6333/snapshots/{snapshot_name} --output full_backup.snapshot
```

### Recovery Procedures
```bash
# Stop Qdrant
docker stop loni-qdrant

# Restore from snapshot
curl -X PUT http://localhost:6333/collections/{collection}/snapshots/upload \
  -F "snapshot=@backup.snapshot"

# Restart service
docker start loni-qdrant
```

## Monitoring and Metrics

### Key Metrics to Monitor
- **Collection Count**: Number of active collections
- **Vector Count**: Total vectors stored
- **Memory Usage**: RAM consumption
- **Search Latency**: Query response times
- **Index Status**: Indexing progress and health
- **Storage Usage**: Disk space consumption

### Prometheus Integration
Qdrant exposes metrics at `/metrics` endpoint:
- Collection statistics
- Search performance metrics
- Memory usage metrics
- Index building metrics
- API request metrics

### Critical Alerts
```yaml
# High memory usage
- alert: QdrantMemoryHigh
  expr: qdrant_memory_used_bytes / qdrant_memory_total_bytes > 0.9
  for: 5m

# Search latency high
- alert: QdrantSearchSlow
  expr: histogram_quantile(0.95, qdrant_search_duration_seconds) > 1.0
  for: 10m

# Collection unavailable
- alert: QdrantCollectionDown
  expr: qdrant_collection_status != 1
  for: 2m
```

## Integration with LONI Services

### Backend API Integration
```python
# Qdrant client configuration
from qdrant_client import QdrantClient

client = QdrantClient(
    host="qdrant",
    port=6333,
    api_key=os.environ.get("QDRANT_API_KEY")
)

# Create collection
client.create_collection(
    collection_name="embeddings",
    vectors_config=VectorParams(size=1536, distance=Distance.COSINE)
)

# Insert vectors
client.upsert(
    collection_name="embeddings",
    points=[
        PointStruct(
            id=1,
            vector=embedding_vector,
            payload={"text": "content", "metadata": {...}}
        )
    ]
)

# Search vectors
results = client.search(
    collection_name="embeddings",
    query_vector=query_embedding,
    limit=10,
    with_payload=True
)
```

### AI Service Integration
```python
# OpenAI embeddings with Qdrant
import openai
from qdrant_client import QdrantClient

# Generate embedding
response = openai.Embedding.create(
    input="text to embed",
    model="text-embedding-ada-002"
)
embedding = response['data'][0]['embedding']

# Store in Qdrant
client.upsert(
    collection_name="documents",
    points=[PointStruct(id=doc_id, vector=embedding, payload=metadata)]
)
```

## Operational Procedures

### Startup Sequence
1. Container initialization
2. Configuration validation
3. Storage directory setup
4. Service binding (HTTP/gRPC)
5. Collection loading
6. Index initialization
7. Health check validation

### Maintenance Tasks
- **Daily**: Monitor collection sizes and performance
- **Weekly**: Optimize collections and clean up deleted vectors
- **Monthly**: Review storage usage and capacity planning
- **Quarterly**: Performance tuning and configuration review

### Scaling Considerations
- **Vertical Scaling**: Increase memory and CPU for single-node performance
- **Horizontal Scaling**: Qdrant cluster mode for distributed deployment
- **Collection Sharding**: Distribute large collections across nodes
- **Read Replicas**: Scale read operations with replica nodes
- **Index Optimization**: Tune HNSW parameters for workload characteristics

{"dashboard": {"id": null, "title": "LONI Database Performance", "tags": ["loni", "database", "performance"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "PostgreSQL Connections", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "pg_stat_database_numbackends", "refId": "A", "legendFormat": "Active Connections"}, {"expr": "pg_settings_max_connections", "refId": "B", "legendFormat": "Max Connections"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}}, {"id": 2, "title": "Query Performance", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "rate(pg_stat_database_tup_returned[5m])", "refId": "A", "legendFormat": "Rows Returned/sec"}, {"expr": "rate(pg_stat_database_tup_fetched[5m])", "refId": "B", "legendFormat": "Rows Fetched/sec"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "rps"}}}, {"id": 3, "title": "Redis Memory Usage", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "redis_memory_used_bytes", "refId": "A", "legendFormat": "Used Memory"}, {"expr": "redis_memory_max_bytes", "refId": "B", "legendFormat": "Max Memory"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "bytes"}}}, {"id": 4, "title": "Qdrant Collections", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "qdrant_collections_total", "refId": "A", "legendFormat": "Collections"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}}}}]}, "overwrite": true}
# LONI Platform GPU Deployment Script
# Optimized for systems with NVIDIA GPU support

param(
    [switch]$Clean,
    [switch]$Build,
    [switch]$Monitor,
    [string]$Profile = "gpu"
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Red
}

function Test-GPUSupport {
    Write-Status "Checking GPU support..."
    
    try {
        $nvidiaInfo = nvidia-smi 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "NVIDIA GPU detected and drivers are working"
            return $true
        }
    } catch {
        Write-Warning "nvidia-smi not found or failed"
    }
    
    try {
        $dockerInfo = docker info | Select-String "nvidia"
        if ($dockerInfo) {
            Write-Success "Docker NVIDIA runtime detected"
            return $true
        } else {
            Write-Warning "Docker NVIDIA runtime not found"
            return $false
        }
    } catch {
        Write-Error "Failed to check Docker GPU support"
        return $false
    }
}

function Test-DockerGPU {
    Write-Status "Testing Docker GPU functionality..."
    
    try {
        $testResult = docker run --rm --gpus all nvidia/cuda:12.0-base-ubuntu20.04 nvidia-smi 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Docker GPU test passed"
            return $true
        } else {
            Write-Warning "Docker GPU test failed"
            return $false
        }
    } catch {
        Write-Warning "Could not run Docker GPU test"
        return $false
    }
}

function Deploy-Services {
    param([string]$DeployProfile)
    
    Write-Status "Deploying LONI platform with profile: $DeployProfile"
    
    # Clean deployment if requested
    if ($Clean) {
        Write-Status "Cleaning existing deployment..."
        docker compose down --remove-orphans --volumes
        docker system prune -f
    }
    
    # Build if requested
    if ($Build) {
        Write-Status "Building services..."
        docker compose build --no-cache
    }
    
    # Deploy with appropriate profile
    Write-Status "Starting services with profile: $DeployProfile"
    
    if ($Monitor) {
        $deployCommand = "docker compose --profile $DeployProfile --profile monitoring up -d"
    } else {
        $deployCommand = "docker compose --profile $DeployProfile up -d"
    }
    
    Write-Status "Executing: $deployCommand"
    Invoke-Expression $deployCommand
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Services started successfully"
    } else {
        Write-Error "Failed to start services"
        return $false
    }
    
    return $true
}

function Wait-ForServices {
    Write-Status "Waiting for services to become healthy..."
    
    $maxWait = 300  # 5 minutes
    $waited = 0
    $interval = 10
    
    while ($waited -lt $maxWait) {
        Start-Sleep $interval
        $waited += $interval
        
        $status = docker compose ps --format "table {{.Service}}\t{{.Status}}"
        $unhealthy = $status | Select-String "unhealthy|exited|starting"
        
        if (-not $unhealthy) {
            Write-Success "All services are healthy"
            return $true
        }
        
        Write-Status "Waiting for services... ($waited/$maxWait seconds)"
    }
    
    Write-Warning "Timeout waiting for services to become healthy"
    return $false
}

function Show-ServiceStatus {
    Write-Status "Current service status:"
    docker compose ps
    
    Write-Status "Resource usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
}

function Test-OllamaGPU {
    Write-Status "Testing Ollama GPU functionality..."
    
    # Wait for Ollama to be ready
    $maxWait = 120
    $waited = 0
    
    while ($waited -lt $maxWait) {
        try {
            $response = Invoke-RestMethod -Uri "http://localhost:11434/api/tags" -Method Get -TimeoutSec 5
            Write-Success "Ollama is responding"
            break
        } catch {
            Start-Sleep 5
            $waited += 5
            Write-Status "Waiting for Ollama... ($waited/$maxWait seconds)"
        }
    }
    
    if ($waited -ge $maxWait) {
        Write-Warning "Ollama did not respond within timeout"
        return $false
    }
    
    # Check GPU usage
    Write-Status "Checking GPU utilization..."
    try {
        $gpuInfo = nvidia-smi --query-gpu=utilization.gpu,memory.used,memory.total --format=csv,noheader,nounits
        Write-Success "GPU Status: $gpuInfo"
    } catch {
        Write-Warning "Could not check GPU status"
    }
    
    return $true
}

# Main execution
Write-Host "🚀 LONI Platform GPU Deployment" -ForegroundColor $Green
Write-Host "================================" -ForegroundColor $Green
Write-Host ""

# Check prerequisites
if (-not (Test-GPUSupport)) {
    Write-Warning "GPU support not detected. Falling back to CPU profile."
    $Profile = "cpu"
}

if ($Profile -eq "gpu" -and -not (Test-DockerGPU)) {
    Write-Warning "Docker GPU support not working. Falling back to CPU profile."
    $Profile = "cpu"
}

# Deploy services
if (Deploy-Services -DeployProfile $Profile) {
    Write-Status "Deployment initiated successfully"
    
    # Wait for services
    if (Wait-ForServices) {
        Write-Success "All services are running"
        
        # Test Ollama if GPU profile
        if ($Profile -eq "gpu") {
            Test-OllamaGPU
        }
        
        # Show status
        Show-ServiceStatus
        
        Write-Host ""
        Write-Success "🎉 LONI Platform deployed successfully with $Profile profile!"
        Write-Host ""
        Write-Host "🌐 Access URLs:" -ForegroundColor $Blue
        Write-Host "  Frontend:    http://localhost:3000" -ForegroundColor $Blue
        Write-Host "  Backend:     http://localhost:8000" -ForegroundColor $Blue
        Write-Host "  Ollama:      http://localhost:11434" -ForegroundColor $Blue
        Write-Host "  Prometheus:  http://localhost:9090" -ForegroundColor $Blue
        Write-Host "  Grafana:     http://localhost:3001" -ForegroundColor $Blue
        Write-Host ""
        Write-Host "🔧 Management Commands:" -ForegroundColor $Blue
        Write-Host "  Status:      docker compose ps" -ForegroundColor $Blue
        Write-Host "  Logs:        docker compose logs -f [service]" -ForegroundColor $Blue
        Write-Host "  Stop:        docker compose down" -ForegroundColor $Blue
        Write-Host ""
        
        if ($Profile -eq "gpu") {
            Write-Host "🎮 GPU Commands:" -ForegroundColor $Blue
            Write-Host "  GPU Status:  nvidia-smi" -ForegroundColor $Blue
            Write-Host "  Model CLI:   docker exec -it loni-backend python manage.py models select" -ForegroundColor $Blue
            Write-Host ""
        }
        
    } else {
        Write-Error "Some services failed to start properly"
        Show-ServiceStatus
        exit 1
    }
} else {
    Write-Error "Deployment failed"
    exit 1
}

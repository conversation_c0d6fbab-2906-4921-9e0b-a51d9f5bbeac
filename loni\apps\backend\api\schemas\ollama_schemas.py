"""
Ollama API schemas for request/response models.

This module defines Pydantic schemas for Ollama model management API endpoints.
"""

from datetime import datetime
from typing import Dict, List, Optional, Union
from enum import Enum

from pydantic import BaseModel, Field, validator


class ModelStatusEnum(str, Enum):
    """Model status enumeration for API."""
    AVAILABLE = "available"
    DOWNLOADING = "downloading"
    DOWNLOADED = "downloaded"
    ERROR = "error"
    NOT_FOUND = "not_found"


class ModelTypeEnum(str, Enum):
    """Model type enumeration for API."""
    CHAT = "chat"
    EMBEDDING = "embedding"
    VISION = "vision"
    CODE = "code"
    MULTIMODAL = "multimodal"


class AvailableModelResponse(BaseModel):
    """Response schema for available model information."""
    
    name: str = Field(..., description="Model name")
    full_identifier: str = Field(..., description="Full model identifier")
    description: str = Field(..., description="Model description")
    use_case: str = Field(..., description="Primary use case")
    sizes: List[str] = Field(default_factory=list, description="Available sizes/variants")
    capabilities: List[str] = Field(default_factory=list, description="Model capabilities")
    last_updated: Optional[datetime] = Field(default=None, description="Last updated timestamp")
    download_size_gb: Optional[float] = Field(default=None, description="Download size in GB")
    tags: List[str] = Field(default_factory=list, description="Model tags")
    categories: List[str] = Field(default_factory=list, description="Model categories")
    registry_url: str = Field(..., description="Registry URL")
    is_installed: bool = Field(default=False, description="Whether model is installed locally")
    
    class Config:
        json_schema_extra = {
            "example": {
                "name": "llama3.2",
                "full_identifier": "llama3.2:latest",
                "description": "Meta's Llama 3.2 model for general conversation and reasoning",
                "use_case": "Conversational AI",
                "sizes": ["8B", "70B"],
                "capabilities": ["text_generation", "conversation", "reasoning"],
                "last_updated": "2024-01-15T10:30:00Z",
                "download_size_gb": 4.5,
                "tags": ["meta", "llama", "chat"],
                "categories": ["Meta", "Language Model"],
                "registry_url": "https://ollama.com/library/llama3.2",
                "is_installed": False
            }
        }


class AvailableModelsResponse(BaseModel):
    """Response schema for paginated available models."""
    
    models: List[AvailableModelResponse] = Field(..., description="List of available models")
    total: int = Field(..., description="Total number of models")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of models per page")
    total_pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there are more pages")
    has_previous: bool = Field(..., description="Whether there are previous pages")


class InstalledModelResponse(BaseModel):
    """Response schema for installed model information."""
    
    id: str = Field(..., description="Model database ID")
    name: str = Field(..., description="Model name")
    tag: str = Field(..., description="Model tag")
    full_name: str = Field(..., description="Full model name with tag")
    description: Optional[str] = Field(default=None, description="Model description")
    model_type: str = Field(..., description="Model type")
    capabilities: List[str] = Field(default_factory=list, description="Model capabilities")
    parameter_count: Optional[str] = Field(default=None, description="Parameter count")
    download_size_gb: Optional[float] = Field(default=None, description="Download size in GB")
    status: ModelStatusEnum = Field(..., description="Model status")
    is_installed: bool = Field(..., description="Installation status")
    installation_path: Optional[str] = Field(default=None, description="Installation path")
    installed_size_gb: Optional[float] = Field(default=None, description="Installed size in GB")
    usage_count: int = Field(default=0, description="Usage count")
    last_used_at: Optional[datetime] = Field(default=None, description="Last used timestamp")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "name": "llama3.2",
                "tag": "latest",
                "full_name": "llama3.2:latest",
                "description": "Meta's Llama 3.2 model",
                "model_type": "chat",
                "capabilities": ["text_generation", "conversation"],
                "parameter_count": "8B",
                "download_size_gb": 4.5,
                "status": "downloaded",
                "is_installed": True,
                "installation_path": "/data/models/ollama/llama3.2",
                "installed_size_gb": 4.7,
                "usage_count": 15,
                "last_used_at": "2024-01-15T14:30:00Z",
                "created_at": "2024-01-10T09:00:00Z",
                "updated_at": "2024-01-15T14:30:00Z"
            }
        }


class ModelInstallRequest(BaseModel):
    """Request schema for model installation."""
    
    model_id: str = Field(..., description="Model identifier to install")
    force_reinstall: bool = Field(default=False, description="Force reinstall if already installed")
    
    @validator('model_id')
    def validate_model_id(cls, v):
        if not v or not v.strip():
            raise ValueError('Model ID cannot be empty')
        return v.strip()
    
    class Config:
        json_schema_extra = {
            "example": {
                "model_id": "llama3.2:latest",
                "force_reinstall": False
            }
        }


class ModelInstallResponse(BaseModel):
    """Response schema for model installation."""
    
    model_id: str = Field(..., description="Model identifier")
    status: str = Field(..., description="Installation status")
    message: str = Field(..., description="Status message")
    download_url: Optional[str] = Field(default=None, description="WebSocket URL for progress updates")
    
    class Config:
        json_schema_extra = {
            "example": {
                "model_id": "llama3.2:latest",
                "status": "started",
                "message": "Model installation started",
                "download_url": "/api/models/install/llama3.2:latest/progress"
            }
        }


class ModelStatusResponse(BaseModel):
    """Response schema for model status."""
    
    model_id: str = Field(..., description="Model identifier")
    status: ModelStatusEnum = Field(..., description="Model status")
    is_installed: bool = Field(..., description="Installation status")
    download_progress: Optional[Dict] = Field(default=None, description="Download progress information")
    installation_path: Optional[str] = Field(default=None, description="Installation path")
    installed_size_gb: Optional[float] = Field(default=None, description="Installed size in GB")
    last_used_at: Optional[datetime] = Field(default=None, description="Last used timestamp")
    usage_count: int = Field(default=0, description="Usage count")
    metadata: Dict = Field(default_factory=dict, description="Additional metadata")
    message: Optional[str] = Field(default=None, description="Status message")
    
    class Config:
        json_schema_extra = {
            "example": {
                "model_id": "llama3.2:latest",
                "status": "downloading",
                "is_installed": False,
                "download_progress": {
                    "model_name": "llama3.2:latest",
                    "status": "downloading",
                    "completed": 2147483648,
                    "total": 4294967296,
                    "percent": 50.0
                },
                "installation_path": None,
                "installed_size_gb": None,
                "last_used_at": None,
                "usage_count": 0,
                "metadata": {},
                "message": "Downloading model..."
            }
        }


class ModelUninstallResponse(BaseModel):
    """Response schema for model uninstallation."""
    
    model_id: str = Field(..., description="Model identifier")
    success: bool = Field(..., description="Whether uninstallation was successful")
    message: str = Field(..., description="Status message")
    
    class Config:
        json_schema_extra = {
            "example": {
                "model_id": "llama3.2:latest",
                "success": True,
                "message": "Model uninstalled successfully"
            }
        }


class ModelDownloadProgressResponse(BaseModel):
    """Response schema for download progress."""
    
    model_name: str = Field(..., description="Model being downloaded")
    status: str = Field(..., description="Download status")
    completed: int = Field(default=0, description="Bytes completed")
    total: int = Field(default=0, description="Total bytes")
    percent: float = Field(default=0.0, description="Completion percentage")
    digest: Optional[str] = Field(default=None, description="Current digest")
    is_complete: bool = Field(..., description="Whether download is complete")
    
    class Config:
        json_schema_extra = {
            "example": {
                "model_name": "llama3.2:latest",
                "status": "downloading",
                "completed": 2147483648,
                "total": 4294967296,
                "percent": 50.0,
                "digest": "sha256:abc123...",
                "is_complete": False
            }
        }


class ModelSearchRequest(BaseModel):
    """Request schema for model search."""
    
    query: Optional[str] = Field(default=None, description="Search query")
    category: Optional[str] = Field(default=None, description="Category filter")
    model_type: Optional[ModelTypeEnum] = Field(default=None, description="Model type filter")
    page: int = Field(default=1, ge=1, description="Page number")
    page_size: int = Field(default=20, ge=1, le=100, description="Page size")
    force_refresh: bool = Field(default=False, description="Force refresh cache")
    
    class Config:
        json_schema_extra = {
            "example": {
                "query": "llama",
                "category": "Meta",
                "model_type": "chat",
                "page": 1,
                "page_size": 20,
                "force_refresh": False
            }
        }


class ErrorResponse(BaseModel):
    """Standard error response schema."""
    
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict] = Field(default=None, description="Additional error details")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": "ModelNotFound",
                "message": "The specified model was not found",
                "details": {
                    "model_id": "nonexistent:latest",
                    "suggestion": "Check available models using /api/models/available"
                }
            }
        }


class ModelSelectionOption(BaseModel):
    """Schema for CLI model selection options."""
    
    index: int = Field(..., description="Selection index")
    name: str = Field(..., description="Model name")
    description: str = Field(..., description="Model description")
    sizes: List[str] = Field(..., description="Available sizes")
    download_size_gb: Optional[float] = Field(default=None, description="Download size")
    use_case: str = Field(..., description="Primary use case")
    
    class Config:
        json_schema_extra = {
            "example": {
                "index": 1,
                "name": "llama3.2",
                "description": "Meta's Llama 3.2 model for conversation",
                "sizes": ["8B", "70B"],
                "download_size_gb": 4.5,
                "use_case": "Conversational AI"
            }
        }


class ModelSizeOption(BaseModel):
    """Schema for model size selection options."""
    
    size: str = Field(..., description="Parameter count")
    download_size_gb: float = Field(..., description="Download size in GB")
    memory_requirements_gb: float = Field(..., description="Memory requirements in GB")
    recommended: bool = Field(default=False, description="Whether this size is recommended")
    
    class Config:
        json_schema_extra = {
            "example": {
                "size": "8B",
                "download_size_gb": 4.5,
                "memory_requirements_gb": 8.0,
                "recommended": True
            }
        }

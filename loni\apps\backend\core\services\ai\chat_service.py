"""
Chat completion service.

This service handles AI chat completion and response generation
following the Single Responsibility Principle.
"""

from typing import Dict, List, Optional
from uuid import UUID

import openai
import anthropic
from loguru import logger

from core.config import get_settings
from .model_service import ModelService
from .rag_service import RAGService
from integrations.ollama.client import OllamaClient
from integrations.ollama.models import ChatRequest as OllamaChatRequest


class ChatService:
    """Service for handling AI chat completions."""
    
    def __init__(self, model_service: ModelService, rag_service: Optional[RAGService] = None):
        """
        Initialize the chat service.
        
        Args:
            model_service: Service for model management
            rag_service: Service for RAG functionality (optional)
        """
        self.model_service = model_service
        self.rag_service = rag_service
        self.settings = get_settings()
        self._clients: Dict[str, Any] = {}
        self._initialize_clients()
    
    def _initialize_clients(self) -> None:
        """Initialize AI model clients."""
        # OpenAI client
        if self.settings.openai.api_key:
            self._clients['openai'] = openai.AsyncOpenAI(
                api_key=self.settings.openai.api_key
            )

        # Anthropic client
        if self.settings.anthropic.api_key:
            self._clients['anthropic'] = anthropic.AsyncAnthropic(
                api_key=self.settings.anthropic.api_key
            )

        # Ollama client
        try:
            self._clients['ollama'] = OllamaClient(
                base_url=f"http://{self.settings.ollama.host}:{self.settings.ollama.port}"
            )
        except Exception as e:
            logger.warning(f"Failed to initialize Ollama client: {e}")
    
    def _get_model_client(self, provider: str):
        """Get the appropriate model client for a provider."""
        return self._clients.get(provider)
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        model_id: str,
        rag_enabled: bool = False,
        conversation_id: Optional[UUID] = None,
        temperature: float = 0.7
    ) -> str:
        """
        Generate AI response for chat messages.
        
        Args:
            messages: List of conversation messages
            model_id: ID of the model to use
            rag_enabled: Whether to use RAG context
            conversation_id: ID of the conversation (for RAG context)
            temperature: Response randomness (0.0 to 1.0)
            
        Returns:
            Generated response text
            
        Raises:
            ValueError: If generation fails
        """
        try:
            # Get model information
            model_info = await self.model_service.get_model_by_id(model_id)
            if not model_info:
                raise ValueError(f"Model {model_id} not found")
            
            # Get RAG context if enabled
            rag_context = None
            if rag_enabled and self.rag_service and conversation_id:
                current_message = messages[-1]['content'] if messages else ""
                try:
                    rag_context = await self.rag_service.get_context(
                        query=current_message,
                        conversation_id=conversation_id
                    )
                except Exception as e:
                    logger.warning(f"RAG context retrieval failed: {e}")

            # Generate response based on model provider
            provider = self._get_model_provider(model_id)

            if provider == "openai":
                return await self._generate_openai_response(messages, model_id, rag_context, temperature)
            elif provider == "anthropic":
                return await self._generate_anthropic_response(messages, model_id, rag_context, temperature)
            elif provider == "ollama":
                return await self._generate_ollama_response(messages, model_id, rag_context, temperature)
            else:
                raise ValueError(f"Unsupported model provider: {provider}")
            
        except Exception as e:
            logger.error(f"Chat completion failed: {e}")
            raise ValueError(f"AI generation failed: {str(e)}")

    def _get_model_provider(self, model_id: str) -> str:
        """Get the provider for a model ID."""
        if model_id.startswith("gpt-") or model_id.startswith("text-"):
            return "openai"
        elif model_id.startswith("claude-"):
            return "anthropic"
        else:
            return "ollama"  # Default to Ollama for local models

    async def _generate_openai_response(
        self,
        messages: List[Dict[str, str]],
        model_id: str,
        rag_context: Optional[str] = None,
        temperature: float = 0.7
    ) -> str:
        """Generate response using OpenAI."""
        client = self._clients.get('openai')
        if not client:
            raise ValueError("OpenAI client not available")

        # Prepare messages with RAG context
        formatted_messages = self._format_messages_for_openai(messages, rag_context)

        response = await client.chat.completions.create(
            model=model_id,
            messages=formatted_messages,
            temperature=temperature,
            max_tokens=1000
        )

        return response.choices[0].message.content

    async def _generate_anthropic_response(
        self,
        messages: List[Dict[str, str]],
        model_id: str,
        rag_context: Optional[str] = None,
        temperature: float = 0.7
    ) -> str:
        """Generate response using Anthropic."""
        client = self._clients.get('anthropic')
        if not client:
            raise ValueError("Anthropic client not available")

        # Prepare prompt for Anthropic
        prompt = self._format_messages_for_anthropic(messages, rag_context)

        response = await client.messages.create(
            model=model_id,
            max_tokens=1000,
            temperature=temperature,
            messages=[{"role": "user", "content": prompt}]
        )

        return response.content[0].text

    async def _generate_ollama_response(
        self,
        messages: List[Dict[str, str]],
        model_id: str,
        rag_context: Optional[str] = None,
        temperature: float = 0.7
    ) -> str:
        """Generate response using Ollama."""
        client = self._clients.get('ollama')
        if not client:
            raise ValueError("Ollama client not available")

        # Prepare messages with RAG context
        formatted_messages = self._format_messages_for_ollama(messages, rag_context)

        request = OllamaChatRequest(
            model=model_id,
            messages=formatted_messages,
            temperature=temperature
        )

        response = await client.chat(request)
        return response.message.get('content', '')

    def _format_messages_for_openai(
        self,
        messages: List[Dict[str, str]],
        rag_context: Optional[str] = None
    ) -> List[Dict[str, str]]:
        """Format messages for OpenAI API."""
        formatted = []

        # Add system message with RAG context if available
        if rag_context:
            formatted.append({
                "role": "system",
                "content": f"Use the following context to help answer questions:\n\n{rag_context}"
            })

        # Add conversation messages
        for msg in messages:
            formatted.append({
                "role": msg["role"],
                "content": msg["content"]
            })

        return formatted

    def _format_messages_for_anthropic(
        self,
        messages: List[Dict[str, str]],
        rag_context: Optional[str] = None
    ) -> str:
        """Format messages for Anthropic API."""
        prompt_parts = []

        if rag_context:
            prompt_parts.append(f"Context: {rag_context}\n")

        for msg in messages:
            role = msg["role"].title()
            content = msg["content"]
            prompt_parts.append(f"{role}: {content}")

        return "\n\n".join(prompt_parts)

    def _format_messages_for_ollama(
        self,
        messages: List[Dict[str, str]],
        rag_context: Optional[str] = None
    ) -> List[Dict[str, str]]:
        """Format messages for Ollama API."""
        formatted = []

        # Add system message with RAG context if available
        if rag_context:
            formatted.append({
                "role": "system",
                "content": f"Use the following context to help answer questions:\n\n{rag_context}"
            })

        # Add conversation messages
        for msg in messages:
            formatted.append({
                "role": msg["role"],
                "content": msg["content"]
            })

        return formatted

    def _select_agent_type(self, model_info: Dict, rag_enabled: bool) -> str:
        """
        Select the appropriate agent type based on model and context.
        
        Args:
            model_info: Model configuration
            rag_enabled: Whether RAG is enabled
            
        Returns:
            Agent type key
        """
        if rag_enabled:
            return 'rag'
        
        capabilities = model_info.get('capabilities', [])
        
        if 'code' in capabilities and len(capabilities) == 1:
            return 'code'
        elif 'analysis' in capabilities and model_info.get('provider') == 'anthropic':
            return 'analysis'
        else:
            return 'chat'
    
    def _prepare_prompt(
        self, 
        messages: List[Dict[str, str]], 
        rag_context: Optional[str] = None
    ) -> str:
        """
        Prepare the prompt for the AI agent.
        
        Args:
            messages: Conversation messages
            rag_context: RAG context if available
            
        Returns:
            Formatted prompt
        """
        if not messages:
            return ""
        
        current_message = messages[-1]['content']
        
        if rag_context:
            return f"""Context from knowledge base:
{rag_context}

Conversation history:
{self._format_conversation_history(messages[:-1])}

Current question: {current_message}

Please answer based on the provided context and conversation history."""
        else:
            return f"""Conversation history:
{self._format_conversation_history(messages[:-1])}

Current message: {current_message}"""
    
    def _format_conversation_history(self, messages: List[Dict[str, str]]) -> str:
        """
        Format conversation history for the prompt.
        
        Args:
            messages: List of messages
            
        Returns:
            Formatted conversation history
        """
        if not messages:
            return "No previous conversation."
        
        formatted = []
        for msg in messages[-5:]:  # Only use last 5 messages for context
            role = msg['role'].title()
            content = msg['content'][:200] + "..." if len(msg['content']) > 200 else msg['content']
            formatted.append(f"{role}: {content}")
        
        return "\n".join(formatted)
    
    async def generate_title(self, messages: List[Dict[str, str]]) -> str:
        """
        Generate a conversation title based on messages.
        
        Args:
            messages: Conversation messages
            
        Returns:
            Generated title
        """
        if not messages:
            return "New Conversation"
        
        # Get first few user messages to understand the topic
        user_messages = [msg['content'] for msg in messages if msg['role'] == 'user'][:3]
        
        if not user_messages:
            return "New Conversation"
        
        # Use AI to generate a concise title
        prompt = f"""Based on this conversation, generate a short, descriptive title (max 6 words):

Conversation content:
{' '.join(user_messages)}

Generate only the title, no additional text:"""

        try:
            # Use OpenAI for title generation if available
            if 'openai' in self._clients:
                response = await self._clients['openai'].chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.3,
                    max_tokens=20
                )
                title = response.choices[0].message.content.strip().strip('"\'')
                return title[:50]  # Limit title length
            else:
                # Fallback to simple title generation
                first_message = user_messages[0][:30] + "..." if user_messages[0] else "New Conversation"
                return first_message
        except Exception as e:
            logger.warning(f"Title generation failed: {e}")
            return "New Conversation"
    
    def estimate_tokens(self, messages: List[Dict[str, str]]) -> Dict[str, int]:
        """
        Estimate token usage for messages.
        
        Args:
            messages: List of messages
            
        Returns:
            Dictionary with input and estimated output tokens
        """
        # Simple estimation - in production, use proper tokenizer
        total_text = " ".join([msg['content'] for msg in messages])
        input_tokens = len(total_text.split()) * 1.3  # Rough estimation
        estimated_output = min(input_tokens * 0.5, 1000)  # Estimate response length
        
        return {
            'input_tokens': int(input_tokens),
            'estimated_output_tokens': int(estimated_output)
        }
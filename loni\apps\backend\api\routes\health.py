"""
Health check routes for monitoring and container orchestration.
"""
import time
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, status
from sqlalchemy.ext.asyncio import AsyncSession

from core.database import get_database_session
from core.config import get_settings

router = APIRouter(tags=["health"])

start_time = time.time()


@router.get("/health", status_code=status.HTTP_200_OK)
async def health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint.
    
    Returns application health status for container monitoring.
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "uptime": int(time.time() - start_time),
        "service": "loni-backend",
        "version": "1.0.0"
    }


@router.get("/health/detailed", status_code=status.HTTP_200_OK)
async def detailed_health_check(
    session: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Detailed health check with database connectivity.
    
    Returns comprehensive health status including dependencies.
    """
    health_data = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "uptime": int(time.time() - start_time),
        "service": "loni-backend",
        "version": "1.0.0",
        "checks": {
            "database": "unknown",
            "memory": "unknown",
            "disk": "unknown"
        }
    }
    
    # Database health check
    try:
        result = await session.execute("SELECT 1")
        if result:
            health_data["checks"]["database"] = "healthy"
        else:
            health_data["checks"]["database"] = "unhealthy"
            health_data["status"] = "degraded"
    except Exception as e:
        health_data["checks"]["database"] = f"unhealthy: {str(e)}"
        health_data["status"] = "unhealthy"
    
    # Memory check (basic)
    try:
        import psutil
        memory = psutil.virtual_memory()
        if memory.percent < 90:
            health_data["checks"]["memory"] = "healthy"
        else:
            health_data["checks"]["memory"] = "warning"
            if health_data["status"] == "healthy":
                health_data["status"] = "degraded"
    except ImportError:
        health_data["checks"]["memory"] = "unavailable"
    
    # Disk check (basic)
    try:
        import psutil
        disk = psutil.disk_usage('/')
        if disk.percent < 90:
            health_data["checks"]["disk"] = "healthy"
        else:
            health_data["checks"]["disk"] = "warning"
            if health_data["status"] == "healthy":
                health_data["status"] = "degraded"
    except ImportError:
        health_data["checks"]["disk"] = "unavailable"
    
    return health_data


@router.get("/ready", status_code=status.HTTP_200_OK)
async def readiness_check(
    session: AsyncSession = Depends(get_async_session)
) -> Dict[str, Any]:
    """
    Readiness check for Kubernetes-style orchestration.
    
    Returns 200 only if the service is ready to receive traffic.
    """
    try:
        # Check database connectivity
        await session.execute("SELECT 1")
        
        # Check critical configuration
        settings = get_settings()
        if not settings.database.url:
            raise ValueError("Database URL not configured")
        
        return {
            "status": "ready",
            "timestamp": datetime.utcnow().isoformat(),
            "service": "loni-backend"
        }
    except Exception as e:
        # Return 503 Service Unavailable if not ready
        from fastapi import HTTPException
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service not ready: {str(e)}"
        )


@router.get("/metrics", status_code=status.HTTP_200_OK)
async def metrics_endpoint() -> Dict[str, Any]:
    """
    Basic metrics endpoint for monitoring systems.
    
    Returns basic application metrics.
    """
    import psutil
    import os
    
    try:
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        return {
            "uptime_seconds": int(time.time() - start_time),
            "memory_usage_bytes": memory_info.rss,
            "memory_usage_mb": round(memory_info.rss / 1024 / 1024, 2),
            "cpu_percent": process.cpu_percent(),
            "open_files": len(process.open_files()),
            "threads": process.num_threads(),
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "error": f"Failed to collect metrics: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }
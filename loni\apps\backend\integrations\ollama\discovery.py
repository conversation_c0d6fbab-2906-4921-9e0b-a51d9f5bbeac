"""
Ollama Model Discovery Service.

This module provides functionality to discover and scrape available models
from the Ollama registry website.
"""

import asyncio
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from urllib.parse import urljoin

import httpx
from bs4 import BeautifulSoup
from loguru import logger
from pydantic import BaseModel, Field

from core.config import get_settings


class DiscoveredModel(BaseModel):
    """Discovered model information from Ollama registry."""
    
    name: str = Field(..., description="Model name")
    full_identifier: str = Field(..., description="Full model identifier")
    description: str = Field(..., description="Model description")
    use_case: str = Field(..., description="Primary use case")
    sizes: List[str] = Field(default_factory=list, description="Available sizes/variants")
    capabilities: List[str] = Field(default_factory=list, description="Model capabilities")
    last_updated: Optional[datetime] = Field(default=None, description="Last updated timestamp")
    download_size_gb: Optional[float] = Field(default=None, description="Download size in GB")
    tags: List[str] = Field(default_factory=list, description="Model tags")
    categories: List[str] = Field(default_factory=list, description="Model categories")
    registry_url: str = Field(..., description="Registry URL")
    metadata: Dict = Field(default_factory=dict, description="Additional metadata")


class ModelDiscoveryService:
    """
    Service for discovering available models from Ollama registry.
    
    Provides caching and structured data extraction from the Ollama website.
    """
    
    def __init__(self):
        """Initialize the discovery service."""
        self.settings = get_settings()
        self.base_url = "https://ollama.com"
        self.search_url = f"{self.base_url}/search"
        self.cache_duration = timedelta(hours=24)
        self._cache: Dict[str, Tuple[datetime, List[DiscoveredModel]]] = {}
        
        # HTTP client with proper headers
        self._client = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0),
            headers={
                "User-Agent": "LONI-Platform/1.0 (Model Discovery Service)",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Accept-Encoding": "gzip, deflate",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            }
        )
    
    async def discover_models(
        self, 
        force_refresh: bool = False,
        search_query: Optional[str] = None,
        category: Optional[str] = None
    ) -> List[DiscoveredModel]:
        """
        Discover available models from Ollama registry.
        
        Args:
            force_refresh: Force refresh cache
            search_query: Optional search query
            category: Optional category filter
            
        Returns:
            List of discovered models
        """
        cache_key = f"{search_query or 'all'}:{category or 'all'}"
        
        # Check cache first
        if not force_refresh and cache_key in self._cache:
            cached_time, cached_models = self._cache[cache_key]
            if datetime.utcnow() - cached_time < self.cache_duration:
                logger.info(f"Returning cached models for key: {cache_key}")
                return cached_models
        
        try:
            logger.info(f"Discovering models from Ollama registry (query: {search_query}, category: {category})")
            
            # Build search URL with parameters
            params = {}
            if search_query:
                params["q"] = search_query
            if category:
                params["c"] = category
            
            # Fetch the search page
            response = await self._client.get(self.search_url, params=params)
            response.raise_for_status()
            
            # Parse the HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            models = await self._parse_models_from_html(soup)
            
            # Cache the results
            self._cache[cache_key] = (datetime.utcnow(), models)
            
            logger.info(f"Discovered {len(models)} models from Ollama registry")
            return models
            
        except Exception as e:
            logger.error(f"Failed to discover models: {e}")
            # Return cached data if available, even if expired
            if cache_key in self._cache:
                _, cached_models = self._cache[cache_key]
                logger.warning("Returning expired cached data due to discovery failure")
                return cached_models
            return []
    
    async def _parse_models_from_html(self, soup: BeautifulSoup) -> List[DiscoveredModel]:
        """
        Parse models from HTML content.
        
        Args:
            soup: BeautifulSoup parsed HTML
            
        Returns:
            List of discovered models
        """
        models = []
        
        try:
            # Find model cards/items (this selector may need adjustment based on actual HTML structure)
            model_elements = soup.find_all(['div', 'article'], class_=re.compile(r'model|card|item'))
            
            if not model_elements:
                # Fallback: look for any elements that might contain model information
                model_elements = soup.find_all(['div', 'article'], attrs={'data-model': True})
            
            if not model_elements:
                # Another fallback: look for links to model pages
                model_links = soup.find_all('a', href=re.compile(r'/library/'))
                model_elements = [link.parent for link in model_links if link.parent]
            
            for element in model_elements:
                try:
                    model = await self._extract_model_info(element)
                    if model:
                        models.append(model)
                except Exception as e:
                    logger.warning(f"Failed to extract model info from element: {e}")
                    continue
            
            # If we didn't find models with the above approach, try a more generic approach
            if not models:
                models = await self._fallback_model_extraction(soup)
            
        except Exception as e:
            logger.error(f"Failed to parse models from HTML: {e}")
        
        return models
    
    async def _extract_model_info(self, element) -> Optional[DiscoveredModel]:
        """
        Extract model information from a single HTML element.
        
        Args:
            element: BeautifulSoup element
            
        Returns:
            DiscoveredModel or None
        """
        try:
            # Extract model name and identifier
            name_element = element.find(['h1', 'h2', 'h3', 'h4'], class_=re.compile(r'title|name|model'))
            if not name_element:
                name_element = element.find('a', href=re.compile(r'/library/'))
            
            if not name_element:
                return None
            
            name = name_element.get_text(strip=True)
            if not name:
                return None
            
            # Clean up the name
            name = re.sub(r'[^\w\-\.]', '', name.lower())
            
            # Extract description
            desc_element = element.find(['p', 'div'], class_=re.compile(r'description|summary'))
            description = desc_element.get_text(strip=True) if desc_element else ""
            
            # Extract model URL
            link_element = element.find('a', href=re.compile(r'/library/'))
            model_url = ""
            if link_element:
                model_url = urljoin(self.base_url, link_element.get('href', ''))
            
            # Extract tags and categories
            tag_elements = element.find_all(['span', 'div'], class_=re.compile(r'tag|category|label'))
            tags = [tag.get_text(strip=True) for tag in tag_elements if tag.get_text(strip=True)]
            
            # Determine capabilities based on tags and description
            capabilities = self._infer_capabilities(description, tags)
            
            # Determine use case
            use_case = self._infer_use_case(description, tags)
            
            # Extract size information (if available)
            sizes = self._extract_sizes(element, description)
            
            # Estimate download size (this would need to be refined)
            download_size = self._estimate_download_size(sizes, description)
            
            return DiscoveredModel(
                name=name,
                full_identifier=f"{name}:latest",
                description=description or f"{name} model",
                use_case=use_case,
                sizes=sizes,
                capabilities=capabilities,
                last_updated=datetime.utcnow(),
                download_size_gb=download_size,
                tags=tags,
                categories=self._categorize_model(description, tags),
                registry_url=model_url,
                metadata={
                    "discovered_at": datetime.utcnow().isoformat(),
                    "source": "ollama_registry"
                }
            )
            
        except Exception as e:
            logger.warning(f"Failed to extract model info: {e}")
            return None
    
    async def _fallback_model_extraction(self, soup: BeautifulSoup) -> List[DiscoveredModel]:
        """
        Fallback method for model extraction when primary method fails.
        
        Args:
            soup: BeautifulSoup parsed HTML
            
        Returns:
            List of discovered models
        """
        models = []
        
        # Look for any text that looks like model names
        text_content = soup.get_text()
        
        # Common model patterns
        model_patterns = [
            r'\b(llama\d*(?:\.\d+)?)\b',
            r'\b(mistral\d*(?:\.\d+)?)\b',
            r'\b(codellama)\b',
            r'\b(vicuna)\b',
            r'\b(alpaca)\b',
            r'\b(falcon)\b',
            r'\b(wizardcoder)\b',
            r'\b(orca)\b',
            r'\b(dolphin)\b',
            r'\b(neural-chat)\b',
            r'\b(starling)\b',
            r'\b(zephyr)\b',
            r'\b(openchat)\b',
            r'\b(nous-hermes)\b',
            r'\b(phind-codellama)\b',
            r'\b(sqlcoder)\b',
            r'\b(magicoder)\b',
            r'\b(deepseek-coder)\b',
            r'\b(nomic-embed-text)\b',
        ]
        
        found_models = set()
        for pattern in model_patterns:
            matches = re.finditer(pattern, text_content, re.IGNORECASE)
            for match in matches:
                model_name = match.group(1).lower()
                found_models.add(model_name)
        
        # Create basic model entries for found models
        for model_name in found_models:
            models.append(DiscoveredModel(
                name=model_name,
                full_identifier=f"{model_name}:latest",
                description=f"{model_name} language model",
                use_case="General purpose",
                sizes=["7B"],  # Default assumption
                capabilities=["text_generation", "conversation"],
                last_updated=datetime.utcnow(),
                download_size_gb=4.0,  # Default estimate
                tags=[],
                categories=["Language Model"],
                registry_url=f"{self.base_url}/library/{model_name}",
                metadata={
                    "discovered_at": datetime.utcnow().isoformat(),
                    "source": "fallback_extraction"
                }
            ))
        
        return models
    
    def _infer_capabilities(self, description: str, tags: List[str]) -> List[str]:
        """Infer model capabilities from description and tags."""
        capabilities = []
        
        text = (description + " " + " ".join(tags)).lower()
        
        if any(word in text for word in ["chat", "conversation", "dialogue"]):
            capabilities.append("conversation")
        if any(word in text for word in ["code", "programming", "coding"]):
            capabilities.append("code_generation")
        if any(word in text for word in ["embed", "embedding", "vector"]):
            capabilities.append("text_embedding")
        if any(word in text for word in ["vision", "image", "visual"]):
            capabilities.append("vision")
        if any(word in text for word in ["instruct", "instruction"]):
            capabilities.append("instruction_following")
        if any(word in text for word in ["reasoning", "logic", "math"]):
            capabilities.append("reasoning")
        
        # Default capability
        if not capabilities:
            capabilities.append("text_generation")
        
        return capabilities
    
    def _infer_use_case(self, description: str, tags: List[str]) -> str:
        """Infer primary use case from description and tags."""
        text = (description + " " + " ".join(tags)).lower()
        
        if any(word in text for word in ["code", "programming", "coding"]):
            return "Code Generation"
        elif any(word in text for word in ["embed", "embedding", "vector"]):
            return "Text Embedding"
        elif any(word in text for word in ["vision", "image", "visual"]):
            return "Vision & Image Analysis"
        elif any(word in text for word in ["chat", "conversation", "assistant"]):
            return "Conversational AI"
        elif any(word in text for word in ["instruct", "instruction"]):
            return "Instruction Following"
        else:
            return "General Purpose"
    
    def _extract_sizes(self, element, description: str) -> List[str]:
        """Extract available model sizes."""
        sizes = []
        
        # Look for size indicators in the element and description
        text = element.get_text() + " " + description
        size_patterns = [
            r'(\d+(?:\.\d+)?)\s*[Bb]',  # 7B, 13B, etc.
            r'(\d+(?:\.\d+)?)\s*billion',
            r'(\d+(?:\.\d+)?)\s*million',
        ]
        
        for pattern in size_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                size = match.group(1)
                if 'billion' in match.group(0).lower() or 'b' in match.group(0).lower():
                    sizes.append(f"{size}B")
                elif 'million' in match.group(0).lower():
                    sizes.append(f"{size}M")
        
        # Default size if none found
        if not sizes:
            sizes = ["7B"]
        
        return list(set(sizes))  # Remove duplicates
    
    def _estimate_download_size(self, sizes: List[str], description: str) -> Optional[float]:
        """Estimate download size based on model sizes."""
        if not sizes:
            return 4.0  # Default estimate
        
        # Simple estimation based on parameter count
        size_estimates = {
            "1B": 0.7,
            "3B": 1.8,
            "7B": 4.0,
            "8B": 4.5,
            "13B": 7.5,
            "30B": 17.0,
            "34B": 19.0,
            "70B": 40.0,
            "180B": 100.0,
        }
        
        # Use the largest size for estimation
        max_size = 0.0
        for size in sizes:
            if size in size_estimates:
                max_size = max(max_size, size_estimates[size])
        
        return max_size if max_size > 0 else 4.0
    
    def _categorize_model(self, description: str, tags: List[str]) -> List[str]:
        """Categorize the model based on description and tags."""
        categories = []
        
        text = (description + " " + " ".join(tags)).lower()
        
        if any(word in text for word in ["llama", "meta"]):
            categories.append("Meta")
        if any(word in text for word in ["mistral"]):
            categories.append("Mistral AI")
        if any(word in text for word in ["code", "programming"]):
            categories.append("Code")
        if any(word in text for word in ["embed", "embedding"]):
            categories.append("Embedding")
        if any(word in text for word in ["vision", "multimodal"]):
            categories.append("Multimodal")
        if any(word in text for word in ["instruct", "chat"]):
            categories.append("Instruction")
        
        # Default category
        if not categories:
            categories.append("Language Model")
        
        return categories
    
    async def close(self) -> None:
        """Close the HTTP client."""
        await self._client.aclose()

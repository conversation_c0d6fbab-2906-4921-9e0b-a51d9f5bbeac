{"dashboard": {"id": null, "title": "LONI Application Performance", "tags": ["loni", "application", "performance"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Response Time", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))", "refId": "A", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))", "refId": "B", "legendFormat": "50th percentile"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "s", "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 0.2}, {"color": "red", "value": 2}]}}}}, {"id": 2, "title": "Error Rate", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m]) * 100", "refId": "A", "legendFormat": "Error Rate %"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent", "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}}, {"id": 3, "title": "Throughput", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "rate(http_requests_total[5m])", "refId": "A", "legendFormat": "{{job}} - {{method}}"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps", "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 100}, {"color": "green", "value": 1000}]}}}}, {"id": 4, "title": "Active Connections", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "sum(http_requests_active)", "refId": "A", "legendFormat": "Active Requests"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 100}, {"color": "red", "value": 500}]}}}}]}, "overwrite": true}
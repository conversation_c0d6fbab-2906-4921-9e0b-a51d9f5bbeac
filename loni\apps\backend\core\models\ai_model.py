"""
AI Model management for the LONI platform.

This module provides models for managing AI models and their capabilities.
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, String, Text, Integer, Float, ForeignKey
from sqlalchemy.dialects.postgresql import <PERSON><PERSON><PERSON>, UUID as PGUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import BaseModel


class ModelProvider(str, Enum):
    """AI model provider enumeration."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    OLLAMA = "ollama"
    HUGGINGFACE = "huggingface"
    GOOGLE = "google"
    COHERE = "cohere"
    MISTRAL = "mistral"


class ModelType(str, Enum):
    """AI model type enumeration."""
    CHAT = "chat"
    COMPLETION = "completion"
    EMBEDDING = "embedding"
    VISION = "vision"
    CODE = "code"
    MULTIMODAL = "multimodal"
    AUDIO = "audio"
    IMAGE = "image"


class ModelStatus(str, Enum):
    """AI model status enumeration."""
    AVAILABLE = "available"
    DOWNLOADING = "downloading"
    DOWNLOADED = "downloaded"
    UNAVAILABLE = "unavailable"
    ERROR = "error"
    DEPRECATED = "deprecated"


class AIModel(BaseModel):
    """
    AI Model registry for managing available models.
    
    Tracks AI models, their capabilities, and availability status.
    """
    
    __tablename__ = "ai_models"
    
    # Basic information
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        unique=True,
        index=True
    )
    
    display_name: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True
    )
    
    # Provider and type
    provider: Mapped[ModelProvider] = mapped_column(
        String(100),
        nullable=False,
        index=True
    )
    
    model_type: Mapped[ModelType] = mapped_column(
        String(100),
        nullable=False,
        index=True
    )
    
    # Capabilities
    capabilities: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False
    )
    
    # Model specifications
    context_length: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True
    )
    
    max_tokens: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True
    )
    
    parameter_count: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True
    )
    
    # Pricing (per 1K tokens)
    input_price: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True
    )
    
    output_price: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True
    )
    
    # Status and availability
    status: Mapped[ModelStatus] = mapped_column(
        String(50),
        default=ModelStatus.AVAILABLE,
        nullable=False,
        index=True
    )
    
    is_enabled: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False
    )
    
    is_local: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False
    )
    
    requires_api_key: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False
    )
    
    # Version and metadata
    version: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True
    )
    
    metadata: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False
    )
    
    # Usage statistics
    usage_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False
    )
    
    last_used_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True
    )
    
    def increment_usage(self) -> None:
        """Increment usage count and update last used timestamp."""
        self.usage_count += 1
        self.last_used_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def enable(self) -> None:
        """Enable the model."""
        self.is_enabled = True
        self.updated_at = datetime.utcnow()
    
    def disable(self) -> None:
        """Disable the model."""
        self.is_enabled = False
        self.updated_at = datetime.utcnow()
    
    def mark_available(self) -> None:
        """Mark model as available."""
        self.status = ModelStatus.AVAILABLE
        self.updated_at = datetime.utcnow()
    
    def mark_downloading(self) -> None:
        """Mark model as downloading."""
        self.status = ModelStatus.DOWNLOADING
        self.updated_at = datetime.utcnow()
    
    def mark_downloaded(self) -> None:
        """Mark model as downloaded."""
        self.status = ModelStatus.DOWNLOADED
        self.updated_at = datetime.utcnow()
    
    def mark_unavailable(self) -> None:
        """Mark model as unavailable."""
        self.status = ModelStatus.UNAVAILABLE
        self.updated_at = datetime.utcnow()
    
    def mark_error(self, error_message: str = "") -> None:
        """Mark model as having an error."""
        self.status = ModelStatus.ERROR
        if error_message:
            self.metadata["error_message"] = error_message
        self.updated_at = datetime.utcnow()
    
    @property
    def full_name(self) -> str:
        """Get full model name with provider."""
        return f"{self.provider}:{self.name}"
    
    @property
    def display_title(self) -> str:
        """Get display title for the model."""
        return self.display_name or self.name
    
    @property
    def is_available(self) -> bool:
        """Check if model is available for use."""
        return self.is_enabled and self.status in [ModelStatus.AVAILABLE, ModelStatus.DOWNLOADED]
    
    @property
    def supports_streaming(self) -> bool:
        """Check if model supports streaming."""
        return self.capabilities.get("streaming", False)
    
    @property
    def supports_vision(self) -> bool:
        """Check if model supports vision/image input."""
        return self.capabilities.get("vision", False) or self.model_type == ModelType.VISION
    
    @property
    def supports_function_calling(self) -> bool:
        """Check if model supports function calling."""
        return self.capabilities.get("function_calling", False)
    
    @property
    def supports_embeddings(self) -> bool:
        """Check if model supports embeddings."""
        return self.model_type == ModelType.EMBEDDING
    
    def get_capability(self, capability: str) -> bool:
        """Get a specific capability."""
        return self.capabilities.get(capability, False)
    
    def add_capability(self, capability: str, value: bool = True) -> None:
        """Add or update a capability."""
        self.capabilities[capability] = value
        self.updated_at = datetime.utcnow()
    
    def remove_capability(self, capability: str) -> None:
        """Remove a capability."""
        if capability in self.capabilities:
            del self.capabilities[capability]
            self.updated_at = datetime.utcnow()
    
    def to_dict(self) -> dict:
        """Convert model to dictionary."""
        return {
            "id": str(self.id),
            "name": self.name,
            "display_name": self.display_name,
            "description": self.description,
            "provider": self.provider,
            "model_type": self.model_type,
            "capabilities": self.capabilities,
            "context_length": self.context_length,
            "max_tokens": self.max_tokens,
            "parameter_count": self.parameter_count,
            "input_price": self.input_price,
            "output_price": self.output_price,
            "status": self.status,
            "is_enabled": self.is_enabled,
            "is_local": self.is_local,
            "requires_api_key": self.requires_api_key,
            "version": self.version,
            "metadata": self.metadata,
            "usage_count": self.usage_count,
            "last_used_at": self.last_used_at.isoformat() if self.last_used_at else None,
            "full_name": self.full_name,
            "display_title": self.display_title,
            "is_available": self.is_available,
            "supports_streaming": self.supports_streaming,
            "supports_vision": self.supports_vision,
            "supports_function_calling": self.supports_function_calling,
            "supports_embeddings": self.supports_embeddings,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }


class OllamaModelRegistry(BaseModel):
    """
    Ollama Model Registry for tracking available and installed models.

    Extends the base AI model with Ollama-specific functionality.
    """

    __tablename__ = "ollama_models"

    # Basic model information
    name: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True
    )

    tag: Mapped[str] = mapped_column(
        String(100),
        default="latest",
        nullable=False
    )

    full_name: Mapped[str] = mapped_column(
        String(355),
        nullable=False,
        unique=True,
        index=True
    )

    # Model metadata from Ollama registry
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True
    )

    model_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        index=True
    )

    capabilities: Mapped[List[str]] = mapped_column(
        JSON,
        default=list,
        nullable=False
    )

    # Size and requirements
    parameter_count: Mapped[Optional[str]] = mapped_column(
        String(20),
        nullable=True
    )

    download_size_gb: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True
    )

    memory_requirements_gb: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True
    )

    # Installation status
    status: Mapped[ModelStatus] = mapped_column(
        String(50),
        default=ModelStatus.AVAILABLE,
        nullable=False,
        index=True
    )

    is_installed: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False
    )

    installation_path: Mapped[Optional[str]] = mapped_column(
        String(500),
        nullable=True
    )

    installed_size_bytes: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True
    )

    # Registry metadata
    registry_url: Mapped[Optional[str]] = mapped_column(
        String(500),
        nullable=True
    )

    last_updated_registry: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True
    )

    # Usage tracking
    download_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False
    )

    usage_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False
    )

    last_used_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True
    )

    # Additional metadata
    metadata: Mapped[dict] = mapped_column(
        JSON,
        default=dict,
        nullable=False
    )

    def mark_downloading(self) -> None:
        """Mark model as downloading."""
        self.status = ModelStatus.DOWNLOADING
        self.updated_at = datetime.utcnow()

    def mark_downloaded(self) -> None:
        """Mark model as downloaded and installed."""
        self.status = ModelStatus.DOWNLOADED
        self.is_installed = True
        self.updated_at = datetime.utcnow()

    def mark_error(self, error_message: str = "") -> None:
        """Mark model as having an error."""
        self.status = ModelStatus.ERROR
        if error_message:
            self.metadata["error_message"] = error_message
        self.updated_at = datetime.utcnow()

    def increment_usage(self) -> None:
        """Increment usage count."""
        self.usage_count += 1
        self.last_used_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()

    def increment_download_count(self) -> None:
        """Increment download count."""
        self.download_count += 1
        self.updated_at = datetime.utcnow()

    @property
    def installed_size_gb(self) -> Optional[float]:
        """Get installed size in GB."""
        if self.installed_size_bytes:
            return round(self.installed_size_bytes / (1024**3), 2)
        return None

    @property
    def is_available_for_download(self) -> bool:
        """Check if model is available for download."""
        return self.status == ModelStatus.AVAILABLE and not self.is_installed

    @property
    def is_ready_for_use(self) -> bool:
        """Check if model is ready for use."""
        return self.is_installed and self.status == ModelStatus.DOWNLOADED

    def to_dict(self) -> dict:
        """Convert model to dictionary."""
        return {
            "id": str(self.id),
            "name": self.name,
            "tag": self.tag,
            "full_name": self.full_name,
            "description": self.description,
            "model_type": self.model_type,
            "capabilities": self.capabilities,
            "parameter_count": self.parameter_count,
            "download_size_gb": self.download_size_gb,
            "memory_requirements_gb": self.memory_requirements_gb,
            "status": self.status,
            "is_installed": self.is_installed,
            "installation_path": self.installation_path,
            "installed_size_bytes": self.installed_size_bytes,
            "installed_size_gb": self.installed_size_gb,
            "registry_url": self.registry_url,
            "last_updated_registry": self.last_updated_registry.isoformat() if self.last_updated_registry else None,
            "download_count": self.download_count,
            "usage_count": self.usage_count,
            "last_used_at": self.last_used_at.isoformat() if self.last_used_at else None,
            "metadata": self.metadata,
            "is_available_for_download": self.is_available_for_download,
            "is_ready_for_use": self.is_ready_for_use,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }

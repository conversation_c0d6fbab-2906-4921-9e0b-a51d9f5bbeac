"""Add Ollama model registry tables

Revision ID: 001_add_ollama_models
Revises: 
Create Date: 2025-07-13 07:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001_add_ollama_models'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create Ollama model registry table."""
    
    # Create ollama_models table
    op.create_table(
        'ollama_models',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        
        # Basic model information
        sa.Column('name', sa.String(255), nullable=False, index=True),
        sa.Column('tag', sa.String(100), nullable=False, default='latest'),
        sa.Column('full_name', sa.String(355), nullable=False, unique=True, index=True),
        
        # Model metadata from Ollama registry
        sa.Column('description', sa.Text, nullable=True),
        sa.Column('model_type', sa.String(50), nullable=False, index=True),
        sa.Column('capabilities', postgresql.JSON, nullable=False, default=list),
        
        # Size and requirements
        sa.Column('parameter_count', sa.String(20), nullable=True),
        sa.Column('download_size_gb', sa.Float, nullable=True),
        sa.Column('memory_requirements_gb', sa.Float, nullable=True),
        
        # Installation status
        sa.Column('status', sa.String(50), nullable=False, default='available', index=True),
        sa.Column('is_installed', sa.Boolean, nullable=False, default=False),
        sa.Column('installation_path', sa.String(500), nullable=True),
        sa.Column('installed_size_bytes', sa.Integer, nullable=True),
        
        # Registry metadata
        sa.Column('registry_url', sa.String(500), nullable=True),
        sa.Column('last_updated_registry', sa.DateTime(timezone=True), nullable=True),
        
        # Usage tracking
        sa.Column('download_count', sa.Integer, nullable=False, default=0),
        sa.Column('usage_count', sa.Integer, nullable=False, default=0),
        sa.Column('last_used_at', sa.DateTime(timezone=True), nullable=True),
        
        # Additional metadata
        sa.Column('metadata', postgresql.JSON, nullable=False, default=dict),
    )
    
    # Create indexes for better query performance
    op.create_index('ix_ollama_models_name_tag', 'ollama_models', ['name', 'tag'])
    op.create_index('ix_ollama_models_status_installed', 'ollama_models', ['status', 'is_installed'])
    op.create_index('ix_ollama_models_model_type', 'ollama_models', ['model_type'])
    op.create_index('ix_ollama_models_usage_count', 'ollama_models', ['usage_count'])
    op.create_index('ix_ollama_models_last_used', 'ollama_models', ['last_used_at'])
    
    # Create trigger to automatically update updated_at timestamp
    op.execute("""
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = now();
            RETURN NEW;
        END;
        $$ language 'plpgsql';
    """)
    
    op.execute("""
        CREATE TRIGGER update_ollama_models_updated_at
        BEFORE UPDATE ON ollama_models
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    """)


def downgrade() -> None:
    """Drop Ollama model registry table."""
    
    # Drop trigger and function
    op.execute("DROP TRIGGER IF EXISTS update_ollama_models_updated_at ON ollama_models;")
    op.execute("DROP FUNCTION IF EXISTS update_updated_at_column();")
    
    # Drop indexes
    op.drop_index('ix_ollama_models_last_used', table_name='ollama_models')
    op.drop_index('ix_ollama_models_usage_count', table_name='ollama_models')
    op.drop_index('ix_ollama_models_model_type', table_name='ollama_models')
    op.drop_index('ix_ollama_models_status_installed', table_name='ollama_models')
    op.drop_index('ix_ollama_models_name_tag', table_name='ollama_models')
    
    # Drop table
    op.drop_table('ollama_models')

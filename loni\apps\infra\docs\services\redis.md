# Redis Cache Service Documentation

## Service Overview

Redis serves as the primary caching layer and session storage for the LONI platform, providing high-performance in-memory data storage with optional persistence. The service uses the official `redis:7-alpine` image, which provides a lightweight, secure, and production-ready Redis 7 installation.

### Role in LONI Platform
- **Session Storage**: User authentication sessions and temporary data
- **Application Cache**: API response caching and computed results
- **Rate Limiting**: API rate limiting and throttling mechanisms
- **Real-time Data**: WebSocket connection management and real-time features
- **Queue Management**: Background job queuing and task processing
- **Temporary Storage**: Short-lived data and intermediate processing results

## Docker Configuration

```yaml
redis:
  image: redis:7-alpine
  container_name: loni-redis
  command: redis-server /usr/local/etc/redis/redis.conf
  environment:
    REDIS_PASSWORD: ${REDIS_PASSWORD}
  volumes:
    - redis_data:/data
    - ./config/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    - ../../data/logs/containers:/var/log/redis
  ports:
    - "${REDIS_PORT:-6379}:6379"
  healthcheck:
    test: ["CMD", "redis-cli", "auth", "${REDIS_PASSWORD}", "ping"]
    interval: 10s
    timeout: 3s
    retries: 5
  restart: unless-stopped
  networks:
    - loni-network
```

## Environment Variables

### Required Variables
| Variable | Description | Default | Security Level |
|----------|-------------|---------|----------------|
| `REDIS_PASSWORD` | Authentication password for Redis | None | **HIGH** |

### Optional Variables
| Variable | Description | Default | Notes |
|----------|-------------|---------|-------|
| `REDIS_PORT` | External port mapping | `6379` | Standard Redis port |
| `REDIS_MAXMEMORY` | Maximum memory usage | System dependent | Memory limit |
| `REDIS_MAXMEMORY_POLICY` | Eviction policy | `allkeys-lru` | Cache eviction strategy |

### Advanced Configuration Variables
| Variable | Description | Default | Use Case |
|----------|-------------|---------|----------|
| `REDIS_DATABASES` | Number of databases | `16` | Multi-tenant separation |
| `REDIS_SAVE` | Persistence configuration | `900 1 300 10 60 10000` | Backup frequency |
| `REDIS_APPENDONLY` | Enable AOF persistence | `yes` | Durability |
| `REDIS_APPENDFSYNC` | AOF sync policy | `everysec` | Performance vs durability |

## Volume Mounts

### Data Persistence
- **Primary Data**: `redis_data:/data`
  - Contains RDB snapshots and AOF files
  - **Backup Strategy**: RDB snapshots + AOF for durability
  - **Size Estimation**: 1-5GB depending on cache size

### Configuration
- **Redis Config**: `./config/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro`
  - Custom Redis configuration file
  - **Read-only**: Prevents runtime modification
  - **Hot Reload**: Requires container restart for changes

### Log Files
- **Container Logs**: `../../data/logs/containers:/var/log/redis`
  - Redis server logs and error messages
  - **Retention**: 7 days, rotated daily
  - **Format**: Redis standard log format

## Network Configuration

### Port Mappings
- **Primary Port**: `6379:6379` (Redis standard)
- **External Access**: Configurable via `REDIS_PORT` environment variable
- **Internal Access**: Available to all services on `loni-network` as `redis:6379`

### Inter-Service Communication
- **Backend API**: Primary connection for caching and sessions
- **Frontend**: Session validation and real-time features
- **Monitoring**: redis-exporter for Prometheus metrics
- **Background Jobs**: Task queue management

## Health Checks

### Primary Health Check
```bash
redis-cli auth ${REDIS_PASSWORD} ping
```
- **Interval**: 10 seconds
- **Timeout**: 3 seconds
- **Retries**: 5 attempts
- **Expected Response**: `PONG`

### Advanced Health Monitoring
```bash
# Memory usage check
redis-cli auth ${REDIS_PASSWORD} info memory

# Connection count
redis-cli auth ${REDIS_PASSWORD} info clients

# Keyspace statistics
redis-cli auth ${REDIS_PASSWORD} info keyspace

# Replication status (if applicable)
redis-cli auth ${REDIS_PASSWORD} info replication
```

## Security Configuration

### Authentication
- **Password Protection**: Required via `REDIS_PASSWORD`
- **No Default Users**: Redis uses simple password authentication
- **ACL Support**: Redis 6+ supports Access Control Lists

### Network Security
- **Internal Only**: No direct external access in production
- **TLS Support**: Configurable via redis.conf
- **Bind Address**: Configured to listen on all interfaces within container

### Recommended Security Hardening
```conf
# redis.conf security settings
requirepass ${REDIS_PASSWORD}
protected-mode yes
bind 0.0.0.0
port 6379

# Disable dangerous commands
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_b835c3f8a5d9e7f2"
```

## Performance Tuning

### Memory Configuration
```conf
# Memory management
maxmemory 512mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Memory optimization
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
set-max-intset-entries 512
```

### Persistence Configuration
```conf
# RDB snapshots
save 900 1      # Save if at least 1 key changed in 900 seconds
save 300 10     # Save if at least 10 keys changed in 300 seconds
save 60 10000   # Save if at least 10000 keys changed in 60 seconds

# AOF persistence
appendonly yes
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
```

### Network and I/O Optimization
```conf
# TCP settings
tcp-keepalive 300
tcp-backlog 511
timeout 0

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60
```

## Troubleshooting

### Common Issues

#### Container Won't Start
```bash
# Check logs
docker logs loni-redis

# Common causes:
# 1. Invalid redis.conf syntax
# 2. Permission issues with data directory
# 3. Port conflicts
# 4. Memory allocation issues
```

#### Connection Issues
```bash
# Test connectivity
docker exec loni-redis redis-cli ping

# Test with authentication
docker exec loni-redis redis-cli auth ${REDIS_PASSWORD} ping

# Check if service is listening
docker exec loni-redis netstat -tlnp | grep 6379
```

#### Memory Issues
```bash
# Check memory usage
docker exec loni-redis redis-cli info memory

# Check eviction statistics
docker exec loni-redis redis-cli info stats | grep evicted

# Monitor memory usage over time
docker exec loni-redis redis-cli --latency-history -i 1
```

#### Performance Issues
```bash
# Monitor slow queries
docker exec loni-redis redis-cli slowlog get 10

# Check client connections
docker exec loni-redis redis-cli info clients

# Monitor command statistics
docker exec loni-redis redis-cli info commandstats
```

## Backup and Recovery

### Automated Backup Strategy
```bash
# RDB snapshot backup
docker exec loni-redis redis-cli auth ${REDIS_PASSWORD} bgsave
docker cp loni-redis:/data/dump.rdb /backups/redis_$(date +%Y%m%d_%H%M%S).rdb

# AOF backup
docker cp loni-redis:/data/appendonly.aof /backups/redis_aof_$(date +%Y%m%d_%H%M%S).aof
```

### Recovery Procedures
```bash
# Stop Redis container
docker stop loni-redis

# Restore RDB file
docker cp /backups/redis_backup.rdb loni-redis:/data/dump.rdb

# Restore AOF file (if using AOF)
docker cp /backups/redis_backup.aof loni-redis:/data/appendonly.aof

# Start container
docker start loni-redis
```

## Monitoring and Metrics

### Key Metrics to Monitor
- **Memory Usage**: Current vs maximum memory
- **Hit Rate**: Cache hit/miss ratio
- **Connection Count**: Active client connections
- **Command Rate**: Operations per second
- **Eviction Rate**: Keys evicted due to memory pressure
- **Persistence Status**: RDB/AOF save status

### Prometheus Integration
The `redis-exporter` service provides metrics for Prometheus monitoring:
- Memory usage metrics
- Command statistics
- Connection metrics
- Persistence metrics
- Keyspace statistics

### Critical Alerts
```yaml
# High memory usage
- alert: RedisMemoryHigh
  expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
  for: 5m

# Low hit rate
- alert: RedisLowHitRate
  expr: rate(redis_keyspace_hits_total[5m]) / (rate(redis_keyspace_hits_total[5m]) + rate(redis_keyspace_misses_total[5m])) < 0.8
  for: 10m

# Too many connections
- alert: RedisTooManyConnections
  expr: redis_connected_clients > 100
  for: 5m
```

## Integration with LONI Services

### Backend API Integration
```python
# Redis connection configuration
REDIS_URL = "redis://:password@redis:6379/0"

# Session storage
SESSION_ENGINE = "django.contrib.sessions.backends.cache"
SESSION_CACHE_ALIAS = "default"

# Cache configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://redis:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'PASSWORD': os.environ.get('REDIS_PASSWORD'),
        }
    }
}
```

### Rate Limiting Integration
```python
# Rate limiting with Redis
RATELIMIT_STORAGE = 'redis://redis:6379/2'
RATELIMIT_STRATEGY = 'moving-window'
```

## Operational Procedures

### Startup Sequence
1. Container initialization
2. Configuration file validation
3. Data directory setup
4. Redis server start
5. Authentication setup
6. Health check validation

### Maintenance Tasks
- **Daily**: Memory usage monitoring
- **Weekly**: Slow query analysis
- **Monthly**: Performance optimization review
- **Quarterly**: Capacity planning and scaling review

### Scaling Considerations
- **Vertical Scaling**: Increase memory allocation
- **Redis Cluster**: For horizontal scaling
- **Read Replicas**: For read-heavy workloads
- **Sentinel**: For high availability
- **Partitioning**: Application-level data partitioning

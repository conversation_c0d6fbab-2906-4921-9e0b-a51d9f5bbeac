# Load environment variables from .env file
# Copy .env.example to .env and update values before running
x-common-env: &common-env
  env_file:
    - .env

services:
  # Frontend Application
  frontend:
    <<: *common-env
    build:
      context: ../frontend
      dockerfile: Dockerfile
      target: production
      cache_from:
        - loni-frontend:latest
    image: loni-frontend:latest
    container_name: loni-frontend
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: http://backend:8000
      NEXT_PUBLIC_WS_URL: ws://backend:8000/ws
      NEXT_PUBLIC_APP_NAME: LONI
    volumes:
      - ../../data/logs/applications:/app/logs
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_started
        restart: true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - loni-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Backend API
  backend:
    <<: *common-env
    build:
      context: ../backend
      dockerfile: Dockerfile
      target: production
      cache_from:
        - loni-backend:latest
    image: loni-backend:latest
    container_name: loni-backend
    environment:
      # Database configuration
      DATABASE_URL: postgresql+asyncpg://${POSTGRES_USER:-loni}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-loni}
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: ${POSTGRES_DB:-loni}
      DATABASE_USER: ${POSTGRES_USER:-loni}
      DATABASE_PASSWORD: ${POSTGRES_PASSWORD}
      
      # AI Services
      OPENAI_API_KEY: ${OPENAI_API_KEY:-}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY:-}
      OLLAMA_BASE_URL: http://ollama:11434
      
      # Vector Database
      QDRANT_URL: http://qdrant:6333
      QDRANT_HOST: qdrant
      QDRANT_PORT: 6333
      
      # Cache
      REDIS_URL: redis://redis:6379
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      
      # Security
      SECRET_KEY: ${JWT_SECRET}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY}
      
      # Application
      APP_HOST: 0.0.0.0
      APP_PORT: 8000
      DEBUG: false
      
      # CORS
      CORS_ORIGINS: '["http://localhost:3000", "http://frontend:3000"]'
      ALLOWED_HOSTS: '["localhost", "backend", "loni-backend"]'
    volumes:
      - ../../data/logs/applications:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
        restart: true
      redis:
        condition: service_healthy
        restart: true
      qdrant:
        condition: service_started
        restart: true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - loni-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: loni-postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-loni}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB:-loni}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init/postgres:/docker-entrypoint-initdb.d
      - ../../data/logs/containers:/var/log/postgres
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-loni}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - loni-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # PostgreSQL Exporter for Prometheus metrics
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: loni-postgres-exporter
    environment:
      DATA_SOURCE_NAME: postgresql://${POSTGRES_USER:-loni}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-loni}?sslmode=disable
    ports:
      - "9187:9187"
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - loni-network

  # Qdrant Vector Database
  qdrant:
    image: qdrant/qdrant:latest
    container_name: loni-qdrant
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
    volumes:
      - qdrant_data:/qdrant/storage
      - ./config/qdrant/config.yaml:/qdrant/config/production.yaml:ro
      - ../../data/logs/containers:/var/log/qdrant
    ports:
      - "${QDRANT_PORT:-6333}:6333"
      - "${QDRANT_GRPC_PORT:-6334}:6334"
    # Temporarily disable health check to allow dependent services to start
    # healthcheck:
    #   test: ["CMD-SHELL", "timeout 10 bash -c '</dev/tcp/localhost/6333' || exit 1"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 5
    #   start_period: 30s
    restart: unless-stopped
    networks:
      - loni-network

  # Redis for Caching and Sessions
  redis:
    image: redis:7-alpine
    container_name: loni-redis
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass ${REDIS_PASSWORD}
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./config/redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
      - ../../data/logs/containers:/var/log/redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "auth", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped
    networks:
      - loni-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis Exporter for Prometheus metrics
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: loni-redis-exporter
    environment:
      REDIS_ADDR: redis://redis:6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    ports:
      - "9121:9121"
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - loni-network

  # Ollama for Local AI Models (with GPU/CPU profiles)
  ollama:
    image: ollama/ollama:latest
    container_name: loni-ollama
    environment:
      OLLAMA_HOST: 0.0.0.0:11434
    volumes:
      - ollama_data:/root/.ollama
      - ./config/ollama:/etc/ollama
      - ../../data/logs/containers:/var/log/ollama
    ports:
      - "${OLLAMA_PORT:-11434}:11434"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - loni-network
    # GPU support (optional, will be ignored if not available)
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    profiles:
      - gpu

  # Ollama without GPU (default profile)
  ollama-cpu:
    image: ollama/ollama:latest
    container_name: loni-ollama-cpu
    environment:
      OLLAMA_HOST: 0.0.0.0:11434
    volumes:
      - ollama_data:/root/.ollama
      - ./config/ollama:/etc/ollama
      - ../../data/logs/containers:/var/log/ollama
    ports:
      - "${OLLAMA_PORT:-11434}:11434"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - loni-network
    profiles:
      - default
      - cpu

  # Neo4j Graph Database (Optional)
  neo4j:
    image: neo4j:5-community
    container_name: loni-neo4j
    environment:
      NEO4J_AUTH: neo4j/${NEO4J_PASSWORD}
      NEO4J_PLUGINS: '["apoc", "graph-data-science"]'
      NEO4J_dbms_security_procedures_unrestricted: apoc.*,gds.*
      NEO4J_dbms_memory_heap_initial__size: 512m
      NEO4J_dbms_memory_heap_max__size: 2G
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    ports:
      - "${NEO4J_HTTP_PORT:-7474}:7474"
      - "${NEO4J_BOLT_PORT:-7687}:7687"
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "${NEO4J_PASSWORD}", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped
    networks:
      - loni-network
    profiles:
      - graph
      - neo4j

  # n8n Workflow Automation
  n8n:
    image: n8nio/n8n:latest
    container_name: loni-n8n
    environment:
      N8N_BASIC_AUTH_ACTIVE: 'true'
      N8N_BASIC_AUTH_USER: ${N8N_USER:-admin}
      N8N_BASIC_AUTH_PASSWORD: ${N8N_PASSWORD}
      N8N_HOST: ${N8N_HOST:-localhost}
      N8N_PORT: 5678
      N8N_PROTOCOL: http
      WEBHOOK_URL: http://${N8N_HOST:-localhost}:5678/
      GENERIC_TIMEZONE: ${TIMEZONE:-UTC}
      DB_TYPE: postgresdb
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: n8n
      DB_POSTGRESDB_USER: ${POSTGRES_USER:-loni}
      DB_POSTGRESDB_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./config/n8n:/etc/n8n
      - ../../data/logs/containers:/var/log/n8n
    ports:
      - "${N8N_PORT:-5678}:5678"
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - loni-network

  # Node Exporter for system metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: loni-node-exporter
    command:
      - '--path.rootfs=/host'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    volumes:
      - /:/host:ro,rslave
    ports:
      - "9100:9100"
    restart: unless-stopped
    networks:
      - loni-network

  # cAdvisor for container metrics
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: loni-cadvisor
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    ports:
      - "8080:8080"
    devices:
      - /dev/kmsg
    restart: unless-stopped
    networks:
      - loni-network
    privileged: true

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: loni-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus/prometheus.fixed.yml:/etc/prometheus/prometheus.yml:ro
      - ./config/prometheus/alert_rules.yml:/etc/prometheus/alert_rules.yml:ro
      - prometheus_data:/prometheus
      - ../../data/logs/containers:/var/log/prometheus
    ports:
      - "${PROMETHEUS_PORT:-9090}:9090"
    depends_on:
      - node-exporter
      - cadvisor
      - postgres-exporter
      - redis-exporter
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - loni-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: loni-grafana
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_SERVER_ROOT_URL: http://${GRAFANA_HOST:-localhost}:${GRAFANA_PORT:-3001}
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards:ro
      - ../../data/logs/containers:/var/log/grafana
    ports:
      - "${GRAFANA_PORT:-3001}:3000"
    depends_on:
      prometheus:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - loni-network

  # Jaeger Tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: loni-jaeger
    environment:
      COLLECTOR_OTLP_ENABLED: 'true'
    volumes:
      - ../../data/logs/containers:/var/log/jaeger
    ports:
      - "${JAEGER_UI_PORT:-16686}:16686"
      - "${JAEGER_GRPC_PORT:-14250}:14250"
      - "${JAEGER_HTTP_PORT:-14268}:14268"
      - "${JAEGER_OTLP_GRPC_PORT:-4317}:4317"
      - "${JAEGER_OTLP_HTTP_PORT:-4318}:4318"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:16686/"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - loni-network

  # Development Tools Logger
  dev-tools-logger:
    build:
      context: ../infra/config/development
      dockerfile: Dockerfile.dev-tools
    container_name: loni-dev-tools-logger
    volumes:
      - ../../data/logs/development:/var/log/loni/development
      - ../frontend:/workspace/frontend:ro
      - ../backend:/workspace/backend:ro
    environment:
      LOG_LEVEL: ERROR
      OUTPUT_FORMAT: json
      WORKSPACE_PATH: /workspace
    restart: unless-stopped
    networks:
      - loni-network
    profiles:
      - development
      - dev-tools

  # Docker Compose Error Monitor
  docker-monitor:
    build:
      context: ./config/docker-monitor
      dockerfile: Dockerfile
    container_name: loni-docker-monitor
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./docker-compose.yml:/app/docker-compose.yml:ro
      - ../../data/logs/docker-compose:/var/log/docker-compose
    environment:
      COMPOSE_PROJECT_NAME: loni
      CHECK_INTERVAL: 30
      LOG_LEVEL: INFO
    restart: unless-stopped
    networks:
      - loni-network
    depends_on:
      - fluent-bit
    profiles:
      - monitoring
      - default

  # Fluent Bit Log Aggregator
  fluent-bit:
    image: fluent/fluent-bit:latest
    container_name: loni-fluent-bit
    volumes:
      - ./config/fluent-bit/fluent-bit.conf:/fluent-bit/etc/fluent-bit.conf:ro
      - ./config/fluent-bit/parsers.conf:/fluent-bit/etc/parsers.conf:ro
      - ./config/fluent-bit/plugins.conf:/fluent-bit/etc/plugins.conf:ro
      - ../../data/logs:/fluent-bit/logs:ro
      - fluent_bit_storage:/tmp/flb-storage
      - ../../data/logs/structured:/fluent-bit/logs/structured
      - ../../data/logs/summary:/fluent-bit/logs/summary
    ports:
      - "24224:24224"
      - "2020:2020"
    environment:
      HOSTNAME: fluent-bit
    restart: unless-stopped
    networks:
      - loni-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: loni-nginx
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx/.htpasswd:/etc/nginx/.htpasswd:ro
      - ./config/nginx/conf.d:/etc/nginx/conf.d:ro
      - nginx_logs:/var/log/nginx
      - ../../data/logs/containers:/var/log/nginx-custom
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    depends_on:
      frontend:
        condition: service_healthy
      backend:
        condition: service_healthy
      grafana:
        condition: service_healthy
      prometheus:
        condition: service_healthy
      n8n:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - loni-network

volumes:
  postgres_data:
    driver: local
  qdrant_data:
    driver: local
  ollama_data:
    driver: local
  redis_data:
    driver: local
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local
  neo4j_import:
    driver: local
  neo4j_plugins:
    driver: local
  n8n_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  nginx_logs:
    driver: local
  fluent_bit_storage:
    driver: local

networks:
  loni-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
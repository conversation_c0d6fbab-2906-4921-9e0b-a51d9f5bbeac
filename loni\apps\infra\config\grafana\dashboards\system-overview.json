{"dashboard": {"id": null, "title": "LONI System Overview", "tags": ["loni", "system", "overview"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Service Status", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"expr": "up", "refId": "A", "legendFormat": "{{job}}"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "mappings": [{"options": {"0": {"text": "DOWN"}}, "type": "value"}, {"options": {"1": {"text": "UP"}}, "type": "value"}]}}}, {"id": 2, "title": "CPU Usage", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"expr": "100 - (avg by(instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "refId": "A", "legendFormat": "CPU Usage %"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent", "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 70}, {"color": "red", "value": 80}]}}}}, {"id": 3, "title": "Memory Usage", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100", "refId": "A", "legendFormat": "Memory Usage %"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent", "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 70}, {"color": "red", "value": 80}]}}}}, {"id": 4, "title": "Disk Usage", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "(1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100", "refId": "A", "legendFormat": "{{mountpoint}}"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "percent", "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}}}}, {"id": 5, "title": "HTTP Request Rate", "type": "timeseries", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}, "targets": [{"expr": "rate(http_requests_total[5m])", "refId": "A", "legendFormat": "{{job}} - {{method}} {{status}}"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "reqps"}}}]}, "overwrite": true}
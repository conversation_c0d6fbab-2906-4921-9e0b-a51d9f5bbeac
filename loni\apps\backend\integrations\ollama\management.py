"""
Ollama Model Management Service.

This module provides comprehensive model management functionality including
installation, removal, status tracking, and metadata management.
"""

import asyncio
import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, AsyncGenerator

from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete

from core.config import get_settings
from core.database import get_database_session
from core.models.ai_model import OllamaModelRegistry, ModelStatus
from .client import OllamaClient
from .discovery import ModelDiscoveryService, DiscoveredModel
from .models import ModelDownloadProgress


class ModelInstallationError(Exception):
    """Exception raised when model installation fails."""
    pass


class ModelManagementService:
    """
    Comprehensive Ollama model management service.
    
    Handles model discovery, installation, removal, and status tracking.
    """
    
    def __init__(self):
        """Initialize the model management service."""
        self.settings = get_settings()
        self.ollama_client = OllamaClient(
            base_url=self.settings.ollama.url
        )
        self.discovery_service = ModelDiscoveryService()

        # Model storage directory from configuration
        self.models_dir = Path(self.settings.ollama.models_path)
        self.models_dir.mkdir(parents=True, exist_ok=True)

        # Download progress tracking
        self._download_progress: Dict[str, ModelDownloadProgress] = {}
        self._download_tasks: Dict[str, asyncio.Task] = {}
    
    async def discover_available_models(
        self,
        force_refresh: bool = False,
        search_query: Optional[str] = None,
        category: Optional[str] = None
    ) -> List[DiscoveredModel]:
        """
        Discover available models from Ollama registry.
        
        Args:
            force_refresh: Force refresh cache
            search_query: Optional search query
            category: Optional category filter
            
        Returns:
            List of discovered models
        """
        try:
            models = await self.discovery_service.discover_models(
                force_refresh=force_refresh,
                search_query=search_query,
                category=category
            )
            
            # Update database with discovered models
            await self._update_registry_with_discovered_models(models)
            
            return models
            
        except Exception as e:
            logger.error(f"Failed to discover models: {e}")
            raise
    
    async def get_installed_models(self) -> List[OllamaModelRegistry]:
        """
        Get all locally installed models.
        
        Returns:
            List of installed models
        """
        try:
            async for session in get_database_session():
                # Get models from database
                result = await session.execute(
                    select(OllamaModelRegistry).where(
                        OllamaModelRegistry.is_installed == True
                    )
                )
                db_models = result.scalars().all()
                
                # Verify with Ollama client
                ollama_models = await self.ollama_client.list_models()
                ollama_names = {model.full_name for model in ollama_models}
                
                # Update database status based on actual Ollama state
                for db_model in db_models:
                    if db_model.full_name not in ollama_names:
                        db_model.is_installed = False
                        db_model.status = ModelStatus.AVAILABLE
                        logger.warning(f"Model {db_model.full_name} not found in Ollama, marking as not installed")
                
                await session.commit()
                
                # Return only actually installed models
                return [model for model in db_models if model.full_name in ollama_names]
                
        except Exception as e:
            logger.error(f"Failed to get installed models: {e}")
            return []
    
    async def get_model_status(self, model_id: str) -> Dict:
        """
        Get detailed status of a specific model.
        
        Args:
            model_id: Model identifier
            
        Returns:
            Model status information
        """
        try:
            async for session in get_database_session():
                # Get model from database
                result = await session.execute(
                    select(OllamaModelRegistry).where(
                        OllamaModelRegistry.full_name == model_id
                    )
                )
                model = result.scalar_one_or_none()
                
                if not model:
                    return {
                        "model_id": model_id,
                        "status": "not_found",
                        "message": "Model not found in registry"
                    }
                
                # Check download progress if downloading
                download_progress = self._download_progress.get(model_id)
                
                # Check with Ollama client
                ollama_models = await self.ollama_client.list_models()
                is_in_ollama = any(m.full_name == model_id for m in ollama_models)
                
                return {
                    "model_id": model_id,
                    "status": model.status,
                    "is_installed": model.is_installed and is_in_ollama,
                    "download_progress": download_progress.dict() if download_progress else None,
                    "installation_path": model.installation_path,
                    "installed_size_gb": model.installed_size_gb,
                    "last_used_at": model.last_used_at.isoformat() if model.last_used_at else None,
                    "usage_count": model.usage_count,
                    "metadata": model.metadata
                }
                
        except Exception as e:
            logger.error(f"Failed to get model status for {model_id}: {e}")
            return {
                "model_id": model_id,
                "status": "error",
                "message": str(e)
            }
    
    async def install_model(self, model_id: str) -> AsyncGenerator[ModelDownloadProgress, None]:
        """
        Install a model with progress tracking.
        
        Args:
            model_id: Model identifier to install
            
        Yields:
            Download progress updates
        """
        try:
            # Check if already downloading
            if model_id in self._download_tasks:
                task = self._download_tasks[model_id]
                if not task.done():
                    raise ModelInstallationError(f"Model {model_id} is already being downloaded")
            
            # Update database status
            await self._update_model_status(model_id, ModelStatus.DOWNLOADING)
            
            logger.info(f"Starting installation of model: {model_id}")
            
            # Start download with progress tracking
            async for progress in self.ollama_client.pull_model(model_id, stream=True):
                # Store progress
                self._download_progress[model_id] = progress
                
                # Yield progress to caller
                yield progress
                
                # Check if complete
                if progress.is_complete:
                    break
            
            # Verify installation
            await self._verify_installation(model_id)
            
            # Update database
            await self._update_model_status(model_id, ModelStatus.DOWNLOADED)
            await self._update_installation_metadata(model_id)
            
            logger.info(f"Successfully installed model: {model_id}")
            
        except Exception as e:
            logger.error(f"Failed to install model {model_id}: {e}")
            await self._update_model_status(model_id, ModelStatus.ERROR, str(e))
            raise ModelInstallationError(f"Installation failed: {e}")
        
        finally:
            # Clean up progress tracking
            self._download_progress.pop(model_id, None)
            self._download_tasks.pop(model_id, None)
    
    async def uninstall_model(self, model_id: str) -> bool:
        """
        Uninstall a model.
        
        Args:
            model_id: Model identifier to uninstall
            
        Returns:
            True if successful
        """
        try:
            logger.info(f"Uninstalling model: {model_id}")
            
            # Remove from Ollama
            success = await self.ollama_client.delete_model(model_id)
            
            if success:
                # Update database
                await self._update_model_status(model_id, ModelStatus.AVAILABLE)
                await self._mark_model_uninstalled(model_id)
                
                logger.info(f"Successfully uninstalled model: {model_id}")
                return True
            else:
                logger.error(f"Failed to uninstall model {model_id} from Ollama")
                return False
                
        except Exception as e:
            logger.error(f"Failed to uninstall model {model_id}: {e}")
            return False
    
    async def get_download_progress(self, model_id: str) -> Optional[ModelDownloadProgress]:
        """
        Get current download progress for a model.
        
        Args:
            model_id: Model identifier
            
        Returns:
            Download progress or None
        """
        return self._download_progress.get(model_id)
    
    async def cancel_download(self, model_id: str) -> bool:
        """
        Cancel an ongoing download.
        
        Args:
            model_id: Model identifier
            
        Returns:
            True if cancelled successfully
        """
        try:
            if model_id in self._download_tasks:
                task = self._download_tasks[model_id]
                if not task.done():
                    task.cancel()
                    
                    # Update status
                    await self._update_model_status(model_id, ModelStatus.AVAILABLE)
                    
                    # Clean up
                    self._download_progress.pop(model_id, None)
                    self._download_tasks.pop(model_id, None)
                    
                    logger.info(f"Cancelled download for model: {model_id}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to cancel download for {model_id}: {e}")
            return False
    
    async def _update_registry_with_discovered_models(self, models: List[DiscoveredModel]) -> None:
        """Update database registry with discovered models."""
        try:
            async for session in get_database_session():
                for discovered_model in models:
                    # Check if model already exists
                    result = await session.execute(
                        select(OllamaModelRegistry).where(
                            OllamaModelRegistry.full_name == discovered_model.full_identifier
                        )
                    )
                    existing_model = result.scalar_one_or_none()
                    
                    if existing_model:
                        # Update existing model
                        existing_model.description = discovered_model.description
                        existing_model.capabilities = discovered_model.capabilities
                        existing_model.download_size_gb = discovered_model.download_size_gb
                        existing_model.last_updated_registry = discovered_model.last_updated
                        existing_model.registry_url = discovered_model.registry_url
                        existing_model.metadata.update(discovered_model.metadata)
                    else:
                        # Create new model entry
                        new_model = OllamaModelRegistry(
                            name=discovered_model.name,
                            tag=discovered_model.full_identifier.split(':')[-1],
                            full_name=discovered_model.full_identifier,
                            description=discovered_model.description,
                            model_type=discovered_model.use_case.lower().replace(' ', '_'),
                            capabilities=discovered_model.capabilities,
                            parameter_count=discovered_model.sizes[0] if discovered_model.sizes else None,
                            download_size_gb=discovered_model.download_size_gb,
                            status=ModelStatus.AVAILABLE,
                            is_installed=False,
                            registry_url=discovered_model.registry_url,
                            last_updated_registry=discovered_model.last_updated,
                            metadata=discovered_model.metadata
                        )
                        session.add(new_model)
                
                await session.commit()
                
        except Exception as e:
            logger.error(f"Failed to update registry with discovered models: {e}")
    
    async def _update_model_status(
        self, 
        model_id: str, 
        status: ModelStatus, 
        error_message: str = ""
    ) -> None:
        """Update model status in database."""
        try:
            async for session in get_database_session():
                result = await session.execute(
                    select(OllamaModelRegistry).where(
                        OllamaModelRegistry.full_name == model_id
                    )
                )
                model = result.scalar_one_or_none()
                
                if model:
                    model.status = status
                    if status == ModelStatus.ERROR and error_message:
                        model.metadata["error_message"] = error_message
                    elif status != ModelStatus.ERROR:
                        model.metadata.pop("error_message", None)
                    
                    await session.commit()
                
        except Exception as e:
            logger.error(f"Failed to update model status: {e}")
    
    async def _verify_installation(self, model_id: str) -> None:
        """Verify that model was installed correctly."""
        try:
            models = await self.ollama_client.list_models()
            if not any(model.full_name == model_id for model in models):
                raise ModelInstallationError(f"Model {model_id} not found after installation")
                
        except Exception as e:
            raise ModelInstallationError(f"Installation verification failed: {e}")
    
    async def _update_installation_metadata(self, model_id: str) -> None:
        """Update installation metadata in database."""
        try:
            async for session in get_database_session():
                result = await session.execute(
                    select(OllamaModelRegistry).where(
                        OllamaModelRegistry.full_name == model_id
                    )
                )
                model = result.scalar_one_or_none()
                
                if model:
                    model.is_installed = True
                    model.installation_path = str(self.models_dir / model_id)
                    model.increment_download_count()
                    
                    # Try to get actual size from Ollama
                    try:
                        ollama_models = await self.ollama_client.list_models()
                        ollama_model = next((m for m in ollama_models if m.full_name == model_id), None)
                        if ollama_model and ollama_model.size:
                            model.installed_size_bytes = ollama_model.size
                    except Exception:
                        pass  # Size information is optional
                    
                    await session.commit()
                
        except Exception as e:
            logger.error(f"Failed to update installation metadata: {e}")
    
    async def _mark_model_uninstalled(self, model_id: str) -> None:
        """Mark model as uninstalled in database."""
        try:
            async for session in get_database_session():
                result = await session.execute(
                    select(OllamaModelRegistry).where(
                        OllamaModelRegistry.full_name == model_id
                    )
                )
                model = result.scalar_one_or_none()
                
                if model:
                    model.is_installed = False
                    model.installation_path = None
                    model.installed_size_bytes = None
                    
                    await session.commit()
                
        except Exception as e:
            logger.error(f"Failed to mark model as uninstalled: {e}")
    
    async def close(self) -> None:
        """Close all connections and clean up resources."""
        try:
            await self.ollama_client.close()
            await self.discovery_service.close()
            
            # Cancel any ongoing downloads
            for task in self._download_tasks.values():
                if not task.done():
                    task.cancel()
            
            logger.info("Model management service closed")
            
        except Exception as e:
            logger.error(f"Error closing model management service: {e}")

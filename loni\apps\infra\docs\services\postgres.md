# PostgreSQL Database Service Documentation

## Service Overview

PostgreSQL serves as the primary relational database for the LONI platform, providing persistent storage for application data, user information, workflow configurations, and system metadata. The service uses the official `postgres:15-alpine` image, which provides a lightweight, secure, and production-ready PostgreSQL 15 installation.

### Role in LONI Platform
- **Primary Data Store**: Stores user accounts, conversations, AI model configurations
- **Workflow Storage**: Stores n8n workflow definitions and execution history
- **Session Management**: Supports Redis-backed session storage with database fallback
- **Analytics**: Stores usage metrics and performance data
- **Configuration**: Stores application settings and feature flags

## Docker Configuration

```yaml
postgres:
  image: postgres:15-alpine
  container_name: loni-postgres
  environment:
    POSTGRES_USER: ${POSTGRES_USER:-loni}
    POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    POSTGRES_DB: ${POSTGRES_DB:-loni}
  volumes:
    - postgres_data:/var/lib/postgresql/data
    - ./init/postgres:/docker-entrypoint-initdb.d
    - ../../data/logs/containers:/var/log/postgresql
  ports:
    - "${POSTGRES_PORT:-5432}:5432"
  healthcheck:
    test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-loni}"]
    interval: 10s
    timeout: 5s
    retries: 5
  restart: unless-stopped
  networks:
    - loni-network
  logging:
    driver: "json-file"
    options:
      max-size: "10m"
      max-file: "3"
```

## Environment Variables

### Required Variables
| Variable | Description | Default | Security Level |
|----------|-------------|---------|----------------|
| `POSTGRES_PASSWORD` | Password for the PostgreSQL superuser | None | **CRITICAL** |

### Optional Variables
| Variable | Description | Default | Notes |
|----------|-------------|---------|-------|
| `POSTGRES_USER` | PostgreSQL superuser name | `loni` | Should match application config |
| `POSTGRES_DB` | Default database name | `loni` | Primary application database |
| `POSTGRES_PORT` | External port mapping | `5432` | Standard PostgreSQL port |

### Advanced Configuration Variables
| Variable | Description | Default | Use Case |
|----------|-------------|---------|----------|
| `POSTGRES_INITDB_ARGS` | Arguments for initdb | None | Custom collation, encoding |
| `POSTGRES_INITDB_WALDIR` | WAL directory location | None | Performance optimization |
| `POSTGRES_HOST_AUTH_METHOD` | Authentication method | `md5` | Security configuration |
| `PGDATA` | Data directory path | `/var/lib/postgresql/data` | Custom data location |

## Volume Mounts

### Data Persistence
- **Primary Data**: `postgres_data:/var/lib/postgresql/data`
  - Contains all database files, WAL logs, and configuration
  - **Backup Strategy**: Daily pg_dump + WAL archiving
  - **Size Estimation**: 10GB initial, 1GB/month growth

### Initialization Scripts
- **Init Scripts**: `./init/postgres:/docker-entrypoint-initdb.d`
  - SQL scripts executed on first container start
  - Used for schema creation, user setup, extensions
  - **Execution Order**: Alphabetical by filename

### Log Files
- **Container Logs**: `../../data/logs/containers:/var/log/postgresql`
  - PostgreSQL server logs and error messages
  - **Retention**: 30 days, rotated daily
  - **Format**: Standard PostgreSQL log format

## Network Configuration

### Port Mappings
- **Primary Port**: `5432:5432` (PostgreSQL standard)
- **External Access**: Configurable via `POSTGRES_PORT` environment variable
- **Internal Access**: Available to all services on `loni-network` as `postgres:5432`

### Inter-Service Communication
- **Backend API**: Primary connection for application data
- **n8n Workflows**: Secondary database for workflow storage
- **Monitoring**: postgres-exporter for Prometheus metrics
- **Backup Services**: Automated backup and restore operations

## Health Checks

### Primary Health Check
```bash
pg_isready -U ${POSTGRES_USER:-loni}
```
- **Interval**: 10 seconds
- **Timeout**: 5 seconds
- **Retries**: 5 attempts
- **Purpose**: Verifies PostgreSQL is accepting connections

### Advanced Health Monitoring
```sql
-- Connection count monitoring
SELECT count(*) FROM pg_stat_activity;

-- Database size monitoring
SELECT pg_size_pretty(pg_database_size('loni'));

-- Replication lag (if applicable)
SELECT EXTRACT(EPOCH FROM (now() - pg_last_xact_replay_timestamp()));
```

## Security Configuration

### Authentication
- **Method**: MD5 password authentication (default)
- **Superuser**: Configured via `POSTGRES_USER` and `POSTGRES_PASSWORD`
- **Application User**: Separate user with limited privileges recommended

### Network Security
- **Internal Only**: No direct external access in production
- **SSL/TLS**: Configurable via postgresql.conf
- **Connection Limits**: Set via max_connections parameter

### Recommended Security Hardening
```sql
-- Create application-specific user
CREATE USER loni_app WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE loni TO loni_app;
GRANT USAGE ON SCHEMA public TO loni_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO loni_app;

-- Revoke unnecessary privileges
REVOKE ALL ON SCHEMA public FROM PUBLIC;
```

## Performance Tuning

### Memory Configuration
```conf
# postgresql.conf optimizations
shared_buffers = 256MB                    # 25% of available RAM
effective_cache_size = 1GB                # 75% of available RAM
maintenance_work_mem = 64MB               # For VACUUM, CREATE INDEX
work_mem = 4MB                            # Per-operation memory
```

### Connection Management
```conf
max_connections = 100                     # Adjust based on load
max_prepared_transactions = 0             # Disable if not needed
```

### Write-Ahead Logging (WAL)
```conf
wal_buffers = 16MB                        # WAL buffer size
checkpoint_completion_target = 0.9        # Spread checkpoints
wal_compression = on                      # Compress WAL records
```

## Troubleshooting

### Common Issues

#### Container Won't Start
```bash
# Check logs
docker logs loni-postgres

# Common causes:
# 1. Invalid POSTGRES_PASSWORD
# 2. Data directory permissions
# 3. Port conflicts
```

#### Connection Refused
```bash
# Test connectivity
docker exec loni-postgres pg_isready -U loni

# Check if service is listening
docker exec loni-postgres netstat -tlnp | grep 5432
```

#### Performance Issues
```sql
-- Check active connections
SELECT count(*) FROM pg_stat_activity WHERE state = 'active';

-- Identify slow queries
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC 
LIMIT 10;
```

### Recovery Procedures

#### Database Corruption
```bash
# Stop container
docker stop loni-postgres

# Check data integrity
docker run --rm -v postgres_data:/data postgres:15-alpine pg_checksums -D /data

# Restore from backup if needed
docker run --rm -v postgres_data:/data -v backup_volume:/backup postgres:15-alpine \
  pg_restore -U postgres -d loni /backup/latest.dump
```

## Backup and Recovery

### Automated Backup Strategy
```bash
# Daily full backup
docker exec loni-postgres pg_dump -U loni -Fc loni > /backups/loni_$(date +%Y%m%d).dump

# Continuous WAL archiving
archive_command = 'cp %p /backups/wal/%f'
```

### Point-in-Time Recovery
```bash
# Restore base backup
pg_basebackup -h postgres -D /recovery -U replication -v -P -W

# Apply WAL files
pg_ctl start -D /recovery
```

## Monitoring and Metrics

### Key Metrics to Monitor
- **Connection Count**: Current vs maximum connections
- **Database Size**: Growth rate and disk usage
- **Query Performance**: Slow query identification
- **Replication Lag**: If using streaming replication
- **Lock Contention**: Blocking queries and deadlocks

### Prometheus Integration
The `postgres-exporter` service provides metrics for Prometheus monitoring:
- Connection metrics
- Database size metrics
- Query performance metrics
- System resource usage

## Integration with LONI Services

### Backend API Integration
```python
# Database connection configuration
DATABASE_URL = "postgresql+asyncpg://loni:password@postgres:5432/loni"
```

### n8n Workflow Integration
```yaml
# n8n database configuration
DB_TYPE: postgresdb
DB_POSTGRESDB_HOST: postgres
DB_POSTGRESDB_PORT: 5432
DB_POSTGRESDB_DATABASE: n8n
DB_POSTGRESDB_USER: loni
DB_POSTGRESDB_PASSWORD: ${POSTGRES_PASSWORD}
```

## Operational Procedures

### Startup Sequence
1. Container initialization
2. Data directory validation
3. PostgreSQL server start
4. Health check validation
5. Ready for connections

### Maintenance Tasks
- **Daily**: Automated backups
- **Weekly**: VACUUM and ANALYZE
- **Monthly**: Full database integrity check
- **Quarterly**: Performance review and optimization

### Scaling Considerations
- **Vertical Scaling**: Increase CPU and memory resources
- **Read Replicas**: For read-heavy workloads
- **Connection Pooling**: Use pgbouncer for connection management
- **Partitioning**: For large tables with time-series data

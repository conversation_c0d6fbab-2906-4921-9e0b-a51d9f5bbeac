# Backend API Service Documentation

## Service Overview

The Backend service provides the core API and business logic for the LONI platform, built with FastAPI, Python 3.11, and async/await patterns. It serves as the central hub for all data processing, AI integrations, user management, and system orchestration.

### Role in LONI Platform
- **REST API**: Primary API server for all frontend and external integrations
- **AI Orchestration**: Manages connections to OpenAI, Anthropic, and local AI models
- **Data Processing**: Handles user data, conversations, and AI-generated content
- **Authentication**: User authentication, authorization, and session management
- **WebSocket Server**: Real-time communication for chat and notifications
- **Background Tasks**: Async task processing and job queue management

## Docker Configuration

```yaml
backend:
  build:
    context: ../backend
    dockerfile: Dockerfile
    target: production
    cache_from:
      - loni-backend:latest
  image: loni-backend:latest
  container_name: loni-backend
  environment:
    # Database configuration
    DATABASE_URL: postgresql+asyncpg://${POSTGRES_USER:-loni}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-loni}
    DATABASE_HOST: postgres
    DATABASE_PORT: 5432
    DATABASE_NAME: ${POSTGRES_DB:-loni}
    DATABASE_USER: ${POSTGRES_USER:-loni}
    DATABASE_PASSWORD: ${POSTGRES_PASSWORD}
    
    # AI Services
    OPENAI_API_KEY: ${OPENAI_API_KEY:-}
    ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY:-}
    OLLAMA_BASE_URL: http://ollama:11434
    
    # Vector Database
    QDRANT_URL: http://qdrant:6333
    QDRANT_HOST: qdrant
    QDRANT_PORT: 6333
    
    # Cache
    REDIS_URL: redis://redis:6379
    REDIS_HOST: redis
    REDIS_PORT: 6379
    REDIS_PASSWORD: ${REDIS_PASSWORD}
    
    # Security
    SECRET_KEY: ${JWT_SECRET}
    ENCRYPTION_KEY: ${ENCRYPTION_KEY}
    
    # Application
    APP_HOST: 0.0.0.0
    APP_PORT: 8000
    DEBUG: false
    
    # CORS
    CORS_ORIGINS: '["http://localhost:3000", "http://frontend:3000"]'
    ALLOWED_HOSTS: '["localhost", "backend", "loni-backend"]'
  volumes:
    - ../../data/logs/applications:/app/logs
  ports:
    - "8000:8000"
  depends_on:
    postgres:
      condition: service_healthy
      restart: true
    redis:
      condition: service_healthy
      restart: true
    qdrant:
      condition: service_healthy
      restart: true
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
    interval: 30s
    timeout: 10s
    retries: 3
  restart: unless-stopped
  networks:
    - loni-network
  logging:
    driver: "json-file"
    options:
      max-size: "10m"
      max-file: "3"
```

## Environment Variables

### Database Configuration
| Variable | Description | Default | Security Level |
|----------|-------------|---------|----------------|
| `DATABASE_URL` | Full database connection string | Constructed | **HIGH** |
| `DATABASE_HOST` | PostgreSQL host | `postgres` | Standard |
| `DATABASE_PORT` | PostgreSQL port | `5432` | Standard |
| `DATABASE_NAME` | Database name | `loni` | Standard |
| `DATABASE_USER` | Database username | `loni` | **MEDIUM** |
| `DATABASE_PASSWORD` | Database password | From env | **CRITICAL** |

### AI Service Configuration
| Variable | Description | Default | Security Level |
|----------|-------------|---------|----------------|
| `OPENAI_API_KEY` | OpenAI API authentication | None | **CRITICAL** |
| `ANTHROPIC_API_KEY` | Anthropic API authentication | None | **CRITICAL** |
| `OLLAMA_BASE_URL` | Local Ollama service URL | `http://ollama:11434` | Standard |

### Cache and Storage
| Variable | Description | Default | Security Level |
|----------|-------------|---------|----------------|
| `REDIS_URL` | Redis connection string | `redis://redis:6379` | **MEDIUM** |
| `REDIS_PASSWORD` | Redis authentication | From env | **HIGH** |
| `QDRANT_URL` | Qdrant vector database URL | `http://qdrant:6333` | Standard |

### Security Configuration
| Variable | Description | Default | Security Level |
|----------|-------------|---------|----------------|
| `SECRET_KEY` | JWT signing secret | From env | **CRITICAL** |
| `ENCRYPTION_KEY` | Data encryption key | From env | **CRITICAL** |
| `CORS_ORIGINS` | Allowed CORS origins | Frontend URLs | **MEDIUM** |
| `ALLOWED_HOSTS` | Allowed host headers | Service names | **MEDIUM** |

### Application Settings
| Variable | Description | Default | Notes |
|----------|-------------|---------|-------|
| `APP_HOST` | Bind address | `0.0.0.0` | Container networking |
| `APP_PORT` | Application port | `8000` | FastAPI default |
| `DEBUG` | Debug mode | `false` | Production setting |
| `LOG_LEVEL` | Logging level | `INFO` | DEBUG, INFO, WARNING, ERROR |

## Volume Mounts

### Application Logs
- **Log Directory**: `../../data/logs/applications:/app/logs`
  - Contains API access logs, error logs, and application traces
  - **Retention**: 30 days, rotated daily
  - **Format**: Structured JSON logging with correlation IDs

### Temporary Storage
- **Upload Directory**: `/tmp/uploads` (container-local)
  - Temporary file uploads and processing
  - **Cleanup**: Automatic cleanup after processing
  - **Size Limit**: 100MB per file, 1GB total

## Network Configuration

### Port Mappings
- **Primary Port**: `8000:8000` (FastAPI/Uvicorn server)
- **External Access**: Direct access for development, proxied via Nginx in production
- **Internal Access**: Available on `loni-network` as `backend:8000`

### API Endpoints
- **Health Check**: `GET /health`
- **API Documentation**: `GET /docs` (Swagger UI)
- **OpenAPI Schema**: `GET /openapi.json`
- **Metrics**: `GET /metrics` (Prometheus format)
- **WebSocket**: `WS /ws` (Real-time communication)

### Service Dependencies
- **PostgreSQL**: Primary database connection
- **Redis**: Caching and session storage
- **Qdrant**: Vector database for AI operations
- **Ollama**: Local AI model inference (optional)

## Health Checks

### Primary Health Check
```bash
curl -f http://localhost:8000/health
```
- **Interval**: 30 seconds
- **Timeout**: 10 seconds
- **Retries**: 3 attempts
- **Response**: JSON with service status and dependencies

### Advanced Health Monitoring
```bash
# Detailed health check
curl http://localhost:8000/health/detailed

# Database connectivity
curl http://localhost:8000/health/database

# Cache connectivity
curl http://localhost:8000/health/redis

# Vector database connectivity
curl http://localhost:8000/health/qdrant

# AI services status
curl http://localhost:8000/health/ai-services
```

## Security Configuration

### Authentication and Authorization
```python
# JWT configuration
JWT_ALGORITHM = "HS256"
JWT_EXPIRATION_DELTA = timedelta(hours=24)
JWT_REFRESH_EXPIRATION_DELTA = timedelta(days=7)

# Password hashing
from passlib.context import CryptContext
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
```

### API Security
```python
# CORS configuration
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Rate limiting
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
```

### Data Protection
```python
# Encryption for sensitive data
from cryptography.fernet import Fernet

encryption_key = os.environ["ENCRYPTION_KEY"]
cipher_suite = Fernet(encryption_key)

def encrypt_sensitive_data(data: str) -> str:
    return cipher_suite.encrypt(data.encode()).decode()

def decrypt_sensitive_data(encrypted_data: str) -> str:
    return cipher_suite.decrypt(encrypted_data.encode()).decode()
```

## Performance Optimization

### Async Database Operations
```python
# Async database session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession

engine = create_async_engine(
    DATABASE_URL,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    pool_recycle=3600
)

# Connection pooling
async def get_db_session():
    async with AsyncSession(engine) as session:
        try:
            yield session
        finally:
            await session.close()
```

### Caching Strategy
```python
# Redis caching decorator
from functools import wraps
import redis.asyncio as redis

redis_client = redis.from_url(REDIS_URL)

def cache_result(expiration: int = 3600):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # Try to get from cache
            cached_result = await redis_client.get(cache_key)
            if cached_result:
                return json.loads(cached_result)
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            await redis_client.setex(
                cache_key, 
                expiration, 
                json.dumps(result, default=str)
            )
            return result
        return wrapper
    return decorator
```

### Background Task Processing
```python
# Celery configuration for background tasks
from celery import Celery

celery_app = Celery(
    "loni_backend",
    broker=REDIS_URL,
    backend=REDIS_URL,
    include=["app.tasks"]
)

# Background task example
@celery_app.task
async def process_ai_request(user_id: int, prompt: str):
    # Process AI request asynchronously
    result = await ai_service.generate_response(prompt)
    await notify_user(user_id, result)
    return result
```

## Troubleshooting

### Common Issues

#### Container Won't Start
```bash
# Check logs
docker logs loni-backend

# Common causes:
# 1. Database connection failures
# 2. Missing environment variables
# 3. Import errors in Python code
# 4. Port binding issues
```

#### Database Connection Issues
```bash
# Test database connectivity
docker exec loni-backend python -c "
import asyncpg
import asyncio
async def test():
    conn = await asyncpg.connect('${DATABASE_URL}')
    await conn.close()
    print('Database connection successful')
asyncio.run(test())
"

# Check database logs
docker logs loni-postgres
```

#### Performance Issues
```bash
# Monitor API response times
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8000/health

# Check memory usage
docker stats loni-backend

# Monitor database queries
docker exec loni-postgres psql -U loni -c "
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC 
LIMIT 10;"
```

#### AI Service Integration Issues
```bash
# Test OpenAI connectivity
docker exec loni-backend python -c "
import openai
openai.api_key = '${OPENAI_API_KEY}'
response = openai.Model.list()
print('OpenAI connection successful')
"

# Test Qdrant connectivity
docker exec loni-backend python -c "
from qdrant_client import QdrantClient
client = QdrantClient(host='qdrant', port=6333)
print(client.get_collections())
"
```

## Monitoring and Metrics

### Application Metrics
- **Request Rate**: HTTP requests per second
- **Response Time**: API endpoint latency percentiles
- **Error Rate**: HTTP 4xx/5xx error percentage
- **Database Connections**: Active connection pool usage
- **Cache Hit Rate**: Redis cache effectiveness
- **Background Tasks**: Celery task queue metrics

### Custom Metrics Collection
```python
# Prometheus metrics
from prometheus_client import Counter, Histogram, Gauge

REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
REQUEST_LATENCY = Histogram('http_request_duration_seconds', 'HTTP request latency')
ACTIVE_CONNECTIONS = Gauge('database_connections_active', 'Active database connections')

# Middleware for metrics collection
@app.middleware("http")
async def metrics_middleware(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    
    REQUEST_COUNT.labels(
        method=request.method, 
        endpoint=request.url.path
    ).inc()
    
    REQUEST_LATENCY.observe(time.time() - start_time)
    return response
```

### Error Tracking
```python
# Sentry integration
import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration

sentry_sdk.init(
    dsn=os.environ.get("SENTRY_DSN"),
    integrations=[FastApiIntegration()],
    traces_sample_rate=0.1,
    environment=os.environ.get("ENVIRONMENT", "production")
)
```

## Integration with LONI Services

### Frontend API Integration
```python
# CORS and authentication for frontend
@app.middleware("http")
async def authenticate_request(request: Request, call_next):
    # Skip authentication for health checks
    if request.url.path in ["/health", "/metrics"]:
        return await call_next(request)
    
    # Validate JWT token
    token = request.headers.get("Authorization")
    if token:
        user = await validate_jwt_token(token)
        request.state.user = user
    
    return await call_next(request)
```

### AI Service Integration
```python
# OpenAI integration
import openai

class AIService:
    def __init__(self):
        openai.api_key = os.environ["OPENAI_API_KEY"]
    
    async def generate_response(self, prompt: str, model: str = "gpt-4"):
        response = await openai.ChatCompletion.acreate(
            model=model,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=1000,
            temperature=0.7
        )
        return response.choices[0].message.content

# Vector database integration
from qdrant_client import QdrantClient

class VectorService:
    def __init__(self):
        self.client = QdrantClient(host="qdrant", port=6333)
    
    async def store_embedding(self, text: str, embedding: list, metadata: dict):
        await self.client.upsert(
            collection_name="documents",
            points=[{
                "id": hash(text),
                "vector": embedding,
                "payload": {"text": text, **metadata}
            }]
        )
```

## Operational Procedures

### Deployment Process
1. **Build Stage**: Multi-stage Docker build with dependency caching
2. **Test Stage**: Run unit tests, integration tests, and linting
3. **Database Migration**: Apply database schema changes
4. **Service Start**: Health check validation and dependency verification
5. **Load Balancer Registration**: Register with reverse proxy

### Maintenance Tasks
- **Daily**: Monitor API performance and error rates
- **Weekly**: Database maintenance and query optimization
- **Monthly**: Security updates and dependency upgrades
- **Quarterly**: Performance review and architecture optimization

### Scaling Considerations
- **Horizontal Scaling**: Multiple backend instances with load balancing
- **Database Scaling**: Read replicas and connection pooling
- **Cache Scaling**: Redis cluster for high availability
- **Background Tasks**: Celery worker scaling based on queue depth
- **AI Service Limits**: Rate limiting and quota management for external APIs

# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is the "Lonors" repository containing multiple AI agent learning and demonstration projects:

1. **Context-Engineering-Intro** - Educational materials and templates for context engineering
2. **ai-agent-mastery** - Complete AI agent mastery course with 6 modules (n8n prototyping through production deployment)
3. **local-ai-packaged** - Packaged local AI solution with Docker setup

## Primary Project: AI Agent Mastery Course

The main project is structured in modules from basic prototyping to production deployment:

- **Module 3** (`3_n8n_Agents/`) - No-code prototyping with n8n workflows
- **Module 4** (`4_Pydantic_AI_Agent/`) - Python agent development with Pydantic AI
- **Module 5** (`5_Agent_Application/`) - Full-stack application (React + FastAPI)
- **Module 6** (`6_Agent_Deployment/`) - Production deployment with Docker

## Architecture Overview

### Module 4: Pydantic AI Agent
- **Core agent**: `agent.py` - Main Pydantic AI agent with tools and dependencies
- **Tools**: `tools.py` - Web search, document retrieval, SQL query, code execution tools
- **Prompts**: `prompt.py` - System prompts for agent behavior
- **Clients**: `clients.py` - External service clients (Supabase, embedding models)
- **RAG Pipeline**: Separate document processing system for knowledge ingestion
- **Tests**: Comprehensive test suite using pytest

### Module 5: Full Application
- **Frontend**: React/TypeScript with Vite, Shadcn UI components, Supabase auth
- **Backend**: FastAPI integration with the Pydantic AI agent
- **Database**: Supabase with conversation history, user management

### Module 6: Production Deployment
- **Modular architecture**: Separate containers for agent API, RAG pipeline, frontend
- **Docker Compose**: Multi-service deployment with reverse proxy support
- **Database**: PostgreSQL/Supabase with vector extensions for RAG
- **Observability**: Optional LangFuse integration for agent monitoring

## Common Commands

### Frontend Development (Module 5/6)
```bash
cd ai-agent-mastery/5_Agent_Application/frontend
# or cd ai-agent-mastery/6_Agent_Deployment/frontend
npm install
npm run dev          # Development server
npm run build        # Production build
npm run lint         # ESLint
npm run test         # Playwright tests
npm run test:ui      # Playwright UI mode
```

### Python Agent Development (Module 4/6)
```bash
cd ai-agent-mastery/4_Pydantic_AI_Agent
# or cd ai-agent-mastery/6_Agent_Deployment/backend_agent_api
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or venv\Scripts\activate  # Windows
pip install -r requirements.txt
python -m pytest tests/  # Run tests
uvicorn agent_api:app --reload --port 8001  # For API version
```

### Docker Deployment (Module 6)
```bash
cd ai-agent-mastery/6_Agent_Deployment
python deploy.py --type cloud          # Cloud deployment with Caddy
python deploy.py --type local --project localai  # Local AI integration
docker compose up -d --build           # Manual Docker Compose
docker compose logs -f agent-api       # View specific service logs
```

### Testing
```bash
# Python tests (Module 4/6)
python -m pytest tests/ -v
python -m pytest tests/test_tools.py::test_specific_function

# Frontend tests (Module 5/6)
cd frontend
npx playwright test
npx playwright test --debug
```

## Key Dependencies

### Python Stack
- **pydantic-ai**: Modern AI agent framework with structured outputs
- **fastapi**: High-performance web framework for APIs
- **supabase**: Backend-as-a-service for database and auth
- **mem0ai**: Long-term memory for AI agents
- **openai/anthropic/ollama**: LLM providers
- **pgvector**: Vector database for RAG
- **langfuse**: Agent observability (optional)

### Frontend Stack
- **React 18** with TypeScript and Vite
- **Shadcn UI**: Component library built on Radix UI
- **TanStack Query**: Data fetching and state management
- **Supabase**: Authentication and real-time database
- **Playwright**: End-to-end testing

## Environment Configuration

Each module requires environment variables:
- Copy `.env.example` to `.env` in each component directory
- Configure LLM providers (OpenAI, Anthropic, local Ollama)
- Set up Supabase database credentials
- Optional: Brave API for web search, Google Drive credentials for RAG

## Development Patterns

### Agent Development
- Keep agents modular: separate `agent.py`, `tools.py`, `prompt.py`
- Use dependency injection pattern with `AgentDeps` dataclass
- Implement comprehensive tool functions with proper error handling
- Write tests for all tool functions and agent interactions

### Frontend Development
- Use TypeScript for type safety
- Follow Shadcn UI patterns for consistent components
- Implement proper loading states and error boundaries
- Use React Query for server state management

### Database Schema
- Run SQL scripts in `sql/` directory in order (0-all-tables.sql creates everything)
- Uses row-level security (RLS) for user data isolation
- Vector embeddings stored with pgvector extension
- Conversation and message history with proper relationships

## Testing Strategy

- **Python**: pytest with async support, fixtures for database/API mocking
- **Frontend**: Playwright for E2E testing with comprehensive mocking
- **Integration**: Docker Compose health checks and API endpoint testing
- All components should have unit tests for core functionality

## Deployment Notes

- Modular deployment allows scaling components independently
- Use provided deployment scripts for consistent environments
- Database migrations handled through SQL scripts
- Optional observability with LangFuse for production monitoring
- Supports both cloud deployment and local AI integration
"""
Conversation-related API schemas.
"""
import uuid
from datetime import datetime
from typing import List, Optional, Any, Dict

from pydantic import BaseModel, Field


class MessageCreate(BaseModel):
    """Schema for creating messages."""
    content: str = Field(..., min_length=1, max_length=10000)
    role: str = Field(..., pattern="^(user|assistant|system)$")


class MessageRead(BaseModel):
    """Schema for reading messages."""
    id: uuid.UUID
    conversation_id: uuid.UUID
    role: str
    content: str
    model_name: Optional[str] = None
    tokens_used: Optional[int] = None
    created_at: datetime
    
    class Config:
        from_attributes = True


class ConversationCreate(BaseModel):
    """Schema for creating conversations."""
    title: Optional[str] = "New Conversation"
    model_name: Optional[str] = "gpt-4"
    rag_enabled: Optional[bool] = True
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0)


class ConversationUpdate(BaseModel):
    """Schema for updating conversations."""
    title: Optional[str] = None
    model_name: Optional[str] = None
    rag_enabled: Optional[bool] = None
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0)


class ConversationRead(BaseModel):
    """Schema for reading conversations."""
    id: uuid.UUID
    user_id: uuid.UUID
    title: str
    model_name: str
    rag_enabled: bool
    temperature: float
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ConversationWithMessages(ConversationRead):
    """Schema for conversation with messages."""
    messages: List[MessageRead] = []


class ChatRequest(BaseModel):
    """Schema for chat requests."""
    message: str = Field(..., min_length=1, max_length=10000)
    conversation_id: Optional[uuid.UUID] = None
    model_name: Optional[str] = "gpt-4"
    stream: Optional[bool] = False
    rag_enabled: Optional[bool] = True


class ChatResponse(BaseModel):
    """Schema for chat responses."""
    conversation_id: uuid.UUID
    user_message: MessageRead
    assistant_message: MessageRead


class ConversationStats(BaseModel):
    """Schema for conversation statistics."""
    conversation_id: uuid.UUID
    title: str
    model_name: str
    total_messages: int
    user_messages: int
    assistant_messages: int
    total_tokens: int
    created_at: datetime
    updated_at: datetime
    rag_enabled: bool
    temperature: float


class ModelInfo(BaseModel):
    """Schema for AI model information."""
    id: str
    name: str
    provider: str
    type: str
    context_length: int
    supports_streaming: bool
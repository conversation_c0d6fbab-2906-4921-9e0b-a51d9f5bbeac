#!/bin/bash

# LONI Ollama Integration Test Script
# This script tests the integration between the LONI backend and Ollama service

set -e  # Exit on any error

echo "🧪 LONI Ollama Integration Test Suite"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
BACKEND_URL="http://localhost:8000"
OLLAMA_URL="http://localhost:11434"
TEST_MODEL="llama3.2:latest"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if service is running
check_service() {
    local service_name=$1
    local url=$2
    local endpoint=${3:-""}
    
    print_status "Checking $service_name service..."
    
    if curl -f -s "$url$endpoint" > /dev/null 2>&1; then
        print_success "$service_name is running and accessible"
        return 0
    else
        print_error "$service_name is not accessible at $url$endpoint"
        return 1
    fi
}

# Function to check Docker container status
check_container() {
    local container_name=$1
    
    print_status "Checking Docker container: $container_name"
    
    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$container_name"; then
        local status=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep "$container_name" | awk '{print $2}')
        print_success "Container $container_name is running ($status)"
        return 0
    else
        print_error "Container $container_name is not running"
        return 1
    fi
}

# Function to test API endpoint
test_api_endpoint() {
    local endpoint=$1
    local method=${2:-"GET"}
    local expected_status=${3:-200}
    
    print_status "Testing API endpoint: $method $endpoint"
    
    local response_code
    if [ "$method" = "GET" ]; then
        response_code=$(curl -s -o /dev/null -w "%{http_code}" "$BACKEND_URL$endpoint")
    else
        response_code=$(curl -s -o /dev/null -w "%{http_code}" -X "$method" "$BACKEND_URL$endpoint")
    fi
    
    if [ "$response_code" -eq "$expected_status" ]; then
        print_success "API endpoint $endpoint returned $response_code"
        return 0
    else
        print_error "API endpoint $endpoint returned $response_code, expected $expected_status"
        return 1
    fi
}

# Function to test model discovery
test_model_discovery() {
    print_status "Testing model discovery API..."
    
    local response=$(curl -s "$BACKEND_URL/api/models/available?page=1&page_size=5")
    
    if echo "$response" | jq -e '.models' > /dev/null 2>&1; then
        local model_count=$(echo "$response" | jq '.models | length')
        print_success "Model discovery returned $model_count models"
        
        # Show first few models
        echo "$response" | jq -r '.models[0:3][] | "  - \(.name): \(.description)"' 2>/dev/null || true
        return 0
    else
        print_error "Model discovery API returned invalid response"
        echo "Response: $response"
        return 1
    fi
}

# Function to test CLI tool
test_cli_tool() {
    print_status "Testing CLI model management tool..."
    
    # Test help command
    if docker exec loni-backend python manage.py models --help > /dev/null 2>&1; then
        print_success "CLI tool is accessible"
    else
        print_error "CLI tool is not accessible"
        return 1
    fi
    
    # Test list command
    if docker exec loni-backend python manage.py models list --installed-only > /dev/null 2>&1; then
        print_success "CLI list command works"
    else
        print_warning "CLI list command failed (may be expected if no models installed)"
    fi
    
    return 0
}

# Function to test volume mounts
test_volume_mounts() {
    print_status "Testing volume mounts..."
    
    # Check if model directory exists in backend container
    if docker exec loni-backend ls -la /app/data/models/ollama > /dev/null 2>&1; then
        print_success "Model directory is mounted in backend container"
    else
        print_error "Model directory is not accessible in backend container"
        return 1
    fi
    
    # Check if Ollama data volume exists
    if docker volume ls | grep -q "loni_ollama_data"; then
        print_success "Ollama data volume exists"
    else
        print_error "Ollama data volume not found"
        return 1
    fi
    
    return 0
}

# Function to test network connectivity
test_network_connectivity() {
    print_status "Testing network connectivity between services..."
    
    # Test backend to Ollama connectivity
    if docker exec loni-backend curl -f -s "http://ollama-cpu:11434/api/tags" > /dev/null 2>&1; then
        print_success "Backend can connect to Ollama service"
    else
        print_error "Backend cannot connect to Ollama service"
        return 1
    fi
    
    return 0
}

# Main test execution
main() {
    echo
    print_status "Starting LONI Ollama Integration Tests..."
    echo
    
    # Test 1: Check Docker containers
    echo "📦 Testing Docker Containers"
    echo "----------------------------"
    check_container "loni-backend" || exit 1
    check_container "loni-ollama-cpu" || check_container "loni-ollama" || {
        print_error "No Ollama container is running"
        exit 1
    }
    echo
    
    # Test 2: Check service accessibility
    echo "🌐 Testing Service Accessibility"
    echo "-------------------------------"
    check_service "Backend" "$BACKEND_URL" "/health" || exit 1
    check_service "Ollama" "$OLLAMA_URL" "/api/tags" || exit 1
    echo
    
    # Test 3: Test volume mounts
    echo "💾 Testing Volume Mounts"
    echo "-----------------------"
    test_volume_mounts || exit 1
    echo
    
    # Test 4: Test network connectivity
    echo "🔗 Testing Network Connectivity"
    echo "------------------------------"
    test_network_connectivity || exit 1
    echo
    
    # Test 5: Test API endpoints
    echo "🔌 Testing API Endpoints"
    echo "-----------------------"
    test_api_endpoint "/health" "GET" 200 || exit 1
    test_api_endpoint "/api/models/available" "GET" 200 || exit 1
    test_api_endpoint "/api/models/installed" "GET" 200 || exit 1
    echo
    
    # Test 6: Test model discovery
    echo "🔍 Testing Model Discovery"
    echo "-------------------------"
    test_model_discovery || exit 1
    echo
    
    # Test 7: Test CLI tool
    echo "🖥️  Testing CLI Tool"
    echo "------------------"
    test_cli_tool || exit 1
    echo
    
    # Summary
    echo "✅ All Integration Tests Passed!"
    echo
    print_success "LONI Ollama integration is working correctly"
    echo
    echo "🚀 Next Steps:"
    echo "  1. Install a model: docker exec loni-backend python manage.py models install llama3.2:latest"
    echo "  2. Use interactive selection: docker exec -it loni-backend python manage.py models select"
    echo "  3. Test model via API: curl -X POST $BACKEND_URL/api/models/install -d '{\"model_id\":\"llama3.2:latest\"}'"
    echo
}

# Check if required tools are available
if ! command -v curl &> /dev/null; then
    print_error "curl is required but not installed"
    exit 1
fi

if ! command -v jq &> /dev/null; then
    print_warning "jq is not installed - some tests may show less detailed output"
fi

if ! command -v docker &> /dev/null; then
    print_error "docker is required but not installed"
    exit 1
fi

# Run main test suite
main
